import 'package:flutter/material.dart';

class StoryBackgroundGradient extends StatelessWidget {
  const StoryBackgroundGradient({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final sizeHeight = MediaQuery.of(context).size.height;

    return Container(
      height: sizeHeight,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color.fromARGB(230, 0, 0, 0),
            Color.fromARGB(0, 0, 0, 0),
          ],
        ),
      ),
    );
  }
}
