import 'package:flutter/material.dart';

class HeroBackground extends StatelessWidget {
  final Widget? child;
  final double? height;
  final bool isMobile;

  const HeroBackground({
    this.child,
    this.height,
    this.isMobile = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        gradient: isMobile
            ? LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.centerRight,
                colors: [
                  Color(0xFF4141E1),
                  Color(0xFF8787E1),
                ],
                stops: [0.0005, 1.0006],
              )
            : LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF4141E1),
                  Color(0xFF8787E1),
                ],
                stops: [0.0005, 1.0006],
              ),
      ),
      child: child,
    );
  }
}
