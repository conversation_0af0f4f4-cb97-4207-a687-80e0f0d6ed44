class DigitalOceanConfig {
  final String region;
  final String bucketName;
  final String accessKey;
  final String secretKey;
  final String spacesEndpoint;

  const DigitalOceanConfig({
    required this.region,
    required this.bucketName,
    required this.accessKey,
    required this.secretKey,
    required this.spacesEndpoint,
  });

  factory DigitalOceanConfig.fromEnv() {
    return DigitalOceanConfig(
      region: const String.fromEnvironment('DO_REGION'),
      bucketName: const String.fromEnvironment('DO_BUCKET_NAME'),
      accessKey: const String.fromEnvironment('DO_ACCESS_KEY'),
      secretKey: const String.fromEnvironment('DO_SECRET_KEY'),
      spacesEndpoint: const String.fromEnvironment('DO_SPACES_ENDPOINT'),
    );
  }
}
