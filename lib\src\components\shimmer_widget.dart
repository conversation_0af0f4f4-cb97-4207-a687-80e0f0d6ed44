import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

import '../../theme/color_outlet.dart';

class ShimmerWidget extends StatelessWidget {
  final double height;
  final double width;
  final double? radius;
  final Widget? child;
  final Color? baseColor;
  final Color? highlightColor;
  const ShimmerWidget({super.key, required this.height, required this.width, this.radius, this.child, this.baseColor, this.highlightColor});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Shimmer.fromColors(
        // baseColor: ColorOutlet.surface,
        // highlightColor: ColorOutlet.backgroundContainer,
        baseColor: baseColor ?? ColorOutlet.surface,
        highlightColor: highlightColor ?? ColorOutlet.backgroundContainer,
        child: Container(
          height: height,
          width: width,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(radius ?? 16),
            color: ColorOutlet.surface,
          ),
          child: child,
        ),
      ),
    );
  }
}
