import 'package:flutter/material.dart';

import '../../../../../components/responsive/responsive_scale_layout.dart';
import '../../../../../components/scroll_reveal.dart';
import 'category_item_widget.dart';

class CategoryGrid extends StatelessWidget {
  final bool isMobile;

  const CategoryGrid({this.isMobile = false, super.key});

  @override
  Widget build(BuildContext context) {
    return ResponsiveScaleLayout(
      builder: (context, screenWidth, itemWidth, scaleFactor) {
        return FittedBox(
          fit: BoxFit.contain,
          alignment: Alignment.centerLeft,
          child: Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ScrollReveal(
                    child: Column(
                      children: [
                        SizedBox(height: 128 * scaleFactor),
                        CategoryItemWidget(
                          width: itemWidth,
                          scale: scaleFactor,
                          color: const Color(0xFF999999),
                          title: 'Eletro Lar',
                          imagePath: 'assets/images/category-image-eletroelar.png',
                        ),
                        SizedBox(height: 16 * scaleFactor),
                        CategoryItemWidget(
                          width: itemWidth,
                          paddingTop: 0,
                          scale: scaleFactor,
                          color: const Color(0xFF8FA3CC),
                          title: 'Video Mobile',
                          imagePath: 'assets/images/category-image-vibemobile.png',
                        ),
                        SizedBox(height: isMobile ? 88 * scaleFactor : 144 * scaleFactor),
                      ],
                    ),
                  ),
                  SizedBox(width: 16),
                  ScrollReveal(
                    child: Column(
                      children: [
                        CategoryItemWidget(
                          width: itemWidth,
                          scale: scaleFactor,
                          color: const Color(0xFFB2A17D),
                          title: 'Espaço Decor',
                          imagePath: 'assets/images/category-image-espacobelo.png',
                        ),
                        SizedBox(height: 16 * scaleFactor),
                        CategoryItemWidget(
                          width: itemWidth,
                          paddingTop: 0,
                          scale: scaleFactor,
                          color: const Color(0xFFB88FCC),
                          title: 'Tudo Tech',
                          imagePath: 'assets/images/category-image-tudotech.png',
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
