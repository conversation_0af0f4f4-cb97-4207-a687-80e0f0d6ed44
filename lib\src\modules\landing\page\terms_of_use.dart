import 'package:flutter/material.dart';
import 'package:meta_seo/meta_seo.dart';

import '../../../components/footer_policy_module.dart';
import '../../../components/no_mouse_drag_scroll_behavior.dart';
import '../../../components/responsive/responsive_padding_wrappe.dart';
import '../../../components/seo/seo.dart';
import '../../../theme/color_outlet.dart';
import '../widgets/lading-page/landing_header/landing_header.dart';
import '../widgets/terms_of_use.dart/terms_of_use_body.dart';

class TermsOfUse extends StatelessWidget implements Seo {
  const TermsOfUse({super.key});

  @override
  Widget build(BuildContext context) {
    return ScrollConfiguration(
      behavior: NoMouseDragScrollBehavior(),
      child: SelectionArea(
        child: Scaffold(
          backgroundColor: ColorOutlet.surface,
          body: CustomScrollView(
            slivers: [
              const LandingHeader(),
              const SliverToBoxAdapter(child: ResponsivePaddingWrapper(child: TermsOfUseBody())),
              const SliverToBoxAdapter(child: FooterPolicyModule()),
            ],
          ),
        ),
      ),
    );
  }

  @override
  loadSeo() {
    final meta = MetaSEO();
    meta.ogTitle(ogTitle: 'Termos de Uso - Promobell');
    meta.description(
      description:
          'Os Termos de Uso do Promobell estabelecem as regras para utilização do aplicativo e garantem a proteção dos usuários.',
    );
    meta.keywords(
      keywords:
          'ofertas, cupons de desconto, frete grátis, alertas de preço, produtos em promoção, marketplaces, Amazon, Magalu, Mercado Livre, app de economia, descontos online, notificações de promoções, rede social de ofertas, termos de uso, termos, dados pessoais, segurança, proteção de dados, app de ofertas, alertas inteligentes, o app alerta você economiza, promobell',
    );
  }
}
