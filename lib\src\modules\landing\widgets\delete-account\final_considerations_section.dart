import 'package:flutter/material.dart';
import 'package:promobell_landing/src/components/text_pattern.dart';
import 'package:promobell_landing/src/theme/color_outlet.dart';

class FinalConsiderationsSection extends StatelessWidget {
  final bool isMobile;

  const FinalConsiderationsSection({this.isMobile = false, super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 40),
        TextPattern.customText(
          text: 'Considerações finais',
          color: ColorOutlet.contentPrimary,
          fontWeightOption: FontWeightOption.bold,
          fontSize: 48,
        ),
        const SizedBox(height: 32),
        TextPattern.customRichText(
          fontSize: 16,
          isSelectable: true,
          children: highlightWords(
            text:
                'Esta ação é irreversível. Ao excluir sua conta, todas as suas informações pessoais como: nome, sobrenome, e-mail, data de nascimento, gênero e foto de perfil, serão automaticamente removidas dos nossos sistemas.\n\nAinda assim, algumas informações anonimizadas, como registros de interações e curtidas, poderão ser mantidas exclusivamente para fins estatísticos e analíticos, conforme estabelecido em nossa Política de Privacidade.\n\nO Promobell foi feito para facilitar sua economia. Se um dia quiser voltar, estaremos por aqui com ainda mais ofertas incríveis esperando por você.',
            wordsToHighlight: ['Política de Privacidade'],
            normalStyle: TextStyle(
              color: ColorOutlet.contentSecondary,
              fontSize: 16,
              fontFamily: TextPattern().fontFamily,
              fontWeight: FontWeight.w400,
            ),
            highlightStyle: TextStyle(
              color: ColorOutlet.contentPrimary,
              fontSize: 16,
              fontFamily: TextPattern().fontFamily,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        SizedBox(height: isMobile ? 81 : 156),
      ],
    );
  }
}
