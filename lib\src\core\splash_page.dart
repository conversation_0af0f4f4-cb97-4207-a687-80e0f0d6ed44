import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:lottie/lottie.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../theme/color_outlet.dart';
import '../modules/offers/controllers/offers_controller.dart';
import '../modules/offers/controllers/story/story_controller.dart';
import 'base/controllers/base_controller.dart/base_controller.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  final controller = Modular.get<OffersController>();
  final baseController = Modular.get<BaseController>();
  final storyController = Modular.get<StoryController>();

  @override
  void initState() {
    super.initState();
    _checkAuth();
  }

  Future<void> _checkAuth() async {
    storyController.getAllStories();

    final prefs = await SharedPreferences.getInstance();
    final accessToken = prefs.getString('accessToken');

    await Future.delayed(const Duration(seconds: 3));
    if (mounted) {
      if (accessToken != null) {
        Modular.to.navigate('/home');
      } else {
        Modular.to.navigate('/profile/login');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorOutlet.contentPrimary,
      body: Center(
        child: Lottie.asset(
          'assets/lottie/lottie-logo.json',
          width: 120,
          height: 120,
          fit: BoxFit.cover,
          repeat: true,
          animate: true,
        ),
      ),
    );
  }
}
