import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../../theme/color_outlet.dart';
import '../../../../components/text_pattern.dart';

class EmptyStatePattern extends StatelessWidget {
  final String text;
  final String image;
  final double height;
  final double width;
  final double textSizeBox;
  final double fontSize;
  const EmptyStatePattern({
    super.key,
    required this.text,
    required this.image,
    this.height = 96,
    this.width = 120,
    this.textSizeBox = 300,
    this.fontSize = 16,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          height: height,
          width: width,
          child: SvgPicture.asset(
            image,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: textSizeBox,
          child: TextPattern.customText(
            text: text,
            fontSize: fontSize,
            textAlign: TextAlign.center,
            lineHeight: 1.5,
            color: ColorOutlet.contentSecondary,
          ),
        ),
      ],
    );
  }
}
