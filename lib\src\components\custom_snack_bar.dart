import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:promobell/src/components/text_pattern.dart';
import '../../theme/color_outlet.dart';

class CustomSnackBar extends StatefulWidget {
  final String message;
  final String? icon;
  final Color backgroundColor;
  final Color? textColor;
  final OverlayEntry overlayEntry;
  final bool justTheBottom;
  final bool noIcon;
  final bool noBottomPadding;

  const CustomSnackBar({
    required this.message,
    this.icon,
    required this.overlayEntry,
    this.backgroundColor = ColorOutlet.feedbackSystem,
    this.textColor = Colors.white,
    this.justTheBottom = false,
    this.noIcon = true,
    this.noBottomPadding = false,
    super.key,
  });

  static void show({
    required BuildContext context,
    required String message,
    String? icon,
    bool justTheBottom = false,
    Color backgroundColor = ColorOutlet.feedbackSystem,
    Color? textColor = Colors.white,
    bool noIcon = true,
    bool noBottomPadding = false, // novo argumento
  }) {
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder:
          (context) => CustomSnackBar(
            message: message,
            icon: icon,
            overlayEntry: overlayEntry,
            backgroundColor: backgroundColor,
            textColor: textColor,
            justTheBottom: justTheBottom,
            noIcon: noIcon,
            noBottomPadding: noBottomPadding,
          ),
    );

    overlay.insert(overlayEntry);

    Future.delayed(const Duration(seconds: 4), () {
      if (overlayEntry.mounted) {
        overlayEntry.remove();
      }
    });
  }

  @override
  State<CustomSnackBar> createState() => _CustomSnackBarState();
}

class _CustomSnackBarState extends State<CustomSnackBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _offsetAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _offsetAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: const Offset(0, 0),
    ).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    _controller.forward();

    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        _controller.reverse();
        Future.delayed(const Duration(milliseconds: 200), () {
          if (mounted) {
            widget.overlayEntry.remove();
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  BoxShadow shadowToast = BoxShadow(
    color: Color(0x1A2E334D),
    offset: Offset(0, 16),
    blurRadius: 16,
    spreadRadius: 0,
  );

  @override
  Widget build(BuildContext context) {
    final double bottomPadding =
        MediaQuery.of(context).padding.bottom;
    final double moreBottomPadding = bottomPadding + 104;

    return Positioned(
      left: widget.noIcon ? 16 : 96,
      right: widget.noIcon ? 16 : 96,
      bottom:
          widget.noBottomPadding
              ? bottomPadding
              : (widget.justTheBottom
                  ? bottomPadding
                  : moreBottomPadding),

      child: SlideTransition(
        position: _offsetAnimation,
        child: Container(
          height: 72,
          padding: EdgeInsets.symmetric(horizontal: 24),
          decoration: BoxDecoration(
            color: ColorOutlet.feedbackSystem,
            boxShadow: [shadowToast],
            borderRadius: BorderRadius.circular(24),
          ),
          child: Row(
            children: [
              Visibility(
                visible: widget.noIcon,
                child: SvgPicture.asset(
                  widget.icon ?? '',
                  width: 24,
                  height: 24,
                  colorFilter: ColorFilter.mode(
                    widget.textColor ?? Colors.white,
                    BlendMode.srcIn,
                  ),
                ),
              ),
              Visibility(
                visible: widget.noIcon,
                child: SizedBox(width: 16),
              ),
              Expanded(
                child: TextPattern.customText(
                  text: widget.message,
                  fontSize: 14,
                  color: ColorOutlet.contentTertiary,
                  decoration: TextDecoration.none,
                  textAlign:
                      widget.noIcon
                          ? TextAlign.start
                          : TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
