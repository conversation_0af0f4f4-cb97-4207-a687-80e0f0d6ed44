import 'package:flutter/material.dart';
import 'package:promobell_landing/src/modules/landing/widgets/delete-account/delete_account_instruction_step.dart';
import 'package:promobell_landing/src/modules/landing/widgets/delete-account/final_considerations_section.dart';

import '../../../components/footer_policy_module.dart';
import '../../../components/no_mouse_drag_scroll_behavior.dart';
import '../../../components/responsive/responsive_padding_wrappe.dart';
import '../../../theme/color_outlet.dart';
import '../widgets/lading-page/landing_header/landing_header.dart';

class DeleteAccountPage extends StatelessWidget {
  const DeleteAccountPage({super.key});

  @override
  Widget build(BuildContext context) {
    final isDescktop = MediaQuery.of(context).size.width > 837;

    return ScrollConfiguration(
      behavior: NoMouseDragScrollBehavior(),
      child: <PERSON><PERSON><PERSON>(
        child: Scaffold(
          backgroundColor: ColorOutlet.surface,
          body: CustomScrollView(
            slivers: [
              const LandingHeader(),
              SliverToBoxAdapter(
                child: Container(
                  padding: const EdgeInsets.only(top: 63),
                  child:
                      isDescktop
                          ? ResponsivePaddingWrapper(
                            maxWidth: 800,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                DeleteAccountInstructionStep(
                                  imagePath: 'assets/images/delete-account-001.png',
                                  title: 'Passo 1',
                                  description:
                                      'Antes de continuar, vale lembrar que ao excluir sua conta, você deixará de aproveitar as melhores ofertas, cupons de desconto e frete grátis reunidos em um só lugar. Tudo isso das principais lojas do Brasil, como Amazon, Magalu e Mercado Livre.\n\nSe ainda quiser continuar, vá até o seu perfil no aplicativo Promobell e toque em “Editar perfil”, logo abaixo do seu nome.',
                                ),
                                DeleteAccountInstructionStep(
                                  isInverse: true,
                                  imagePath: 'assets/images/delete-account-002.png',
                                  title: 'Passo 2',
                                  description:
                                      'Nas configurações do perfil, role até o final da tela e toque em “Excluir conta”, logo abaixo do campo de e-mail.\n\nAo fazer isso, você também abrirá mão de recursos como a busca inteligente, que ajuda a encontrar produtos em oferta com rapidez, e das categorias que combina com você.',
                                ),
                                DeleteAccountInstructionStep(
                                  imagePath: 'assets/images/delete-account-003.png',
                                  title: 'Passo 3',
                                  description:
                                      'Para concluir, selecione o motivo da saída e digite a palavra “EXCLUIR” (em letras maiúsculas) no campo indicado.\n\nAo confirmar, você também deixará de receber alertas inteligentes quando um produto do seu interesse entra em promoção, além de perder a experiência de curtir, salvar e compartilhar seus achados favoritos como em uma rede social de ofertas.',
                                ),
                                FinalConsiderationsSection(),
                              ],
                            ),
                          )
                          : ResponsivePaddingWrapper.mobile(
                            maxWidth: 400,
                            minPadding: 32,
                            child: FittedBox(
                              fit: BoxFit.contain,
                              child: SizedBox(
                                width: 400,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    DeleteAccountInstructionStep(
                                      isMobile: true,
                                      imagePath: 'assets/images/delete-account-001.png',
                                      title: 'Passo 1',
                                      description:
                                          'Antes de continuar, vale lembrar que ao excluir sua conta, você deixará de aproveitar as melhores ofertas, cupons de desconto e frete grátis reunidos em um só lugar. Tudo isso das principais lojas do Brasil, como Amazon, Magalu e Mercado Livre.\n\nSe ainda quiser continuar, vá até o seu perfil no aplicativo Promobell e toque em “Editar perfil”, logo abaixo do seu nome.',
                                    ),
                                    DeleteAccountInstructionStep(
                                      isMobile: true,
                                      isInverse: true,
                                      imagePath: 'assets/images/delete-account-002.png',
                                      title: 'Passo 2',
                                      description:
                                          'Nas configurações do perfil, role até o final da tela e toque em “Excluir conta”, logo abaixo do campo de e-mail.\n\nAo fazer isso, você também abrirá mão de recursos como a busca inteligente, que ajuda a encontrar produtos em oferta com rapidez, e das categorias que combina com você.',
                                    ),
                                    DeleteAccountInstructionStep(
                                      isMobile: true,
                                      imagePath: 'assets/images/delete-account-003.png',
                                      title: 'Passo 3',
                                      description:
                                          'Para concluir, selecione o motivo da saída e digite a palavra “EXCLUIR” (em letras maiúsculas) no campo indicado.\n\nAo confirmar, você também deixará de receber alertas inteligentes quando um produto do seu interesse entra em promoção, além de perder a experiência de curtir, salvar e compartilhar seus achados favoritos como em uma rede social de ofertas.',
                                    ),
                                    FinalConsiderationsSection(isMobile: true),
                                  ],
                                ),
                              ),
                            ),
                          ),
                ),
              ),
              const SliverToBoxAdapter(child: FooterPolicyModule()),
            ],
          ),
        ),
      ),
    );
  }
}
