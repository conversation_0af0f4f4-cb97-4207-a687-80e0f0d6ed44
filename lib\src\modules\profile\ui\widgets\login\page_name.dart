import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:promobell/src/components/text_pattern.dart';
import 'package:promobell/src/modules/profile/controllers/profile_controller.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/navigation_buttons_row.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/page_view_template.dart';
import 'package:promobell/theme/svg_icons.dart';
import '../../../../../../theme/color_outlet.dart';

class PageName extends StatefulWidget {
  final VoidCallback onNext;
  final ProfileController controller;

  const PageName({
    required this.controller,
    required this.onNext,
    super.key,
  });

  @override
  State<PageName> createState() => _PageNameState();
}

class _PageNameState extends State<PageName> {
  late TextEditingController nameController;
  late TextEditingController surnameController;
  final nameFocusController = FocusNode();
  final surnameFocusController = FocusNode();
  final formKey = GlobalKey<FormState>();

  void _updateControllerValues() {
    widget.controller.setUserName(
      nameController.text,
      surnameController.text,
    );
  }

  @override
  void initState() {
    nameController = TextEditingController(
      text: widget.controller.userName,
    )..addListener(_updateControllerValues);
    surnameController = TextEditingController(
      text: widget.controller.userSurname,
    )..addListener(_updateControllerValues);
    super.initState();
  }

  @override
  void dispose() {
    nameController.dispose();
    surnameController.dispose();
    nameFocusController.dispose();
    surnameFocusController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final paddingBottom = MediaQuery.of(context).padding.bottom;
    return AnimatedBuilder(
      animation: Listenable.merge([
        nameController,
        surnameController,
        nameFocusController,
        surnameFocusController,
      ]),
      builder: (context, _) {
        return PageViewTemplate(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(height: 160),
              Center(
                child: SvgPicture.asset(
                  SvgIcons.markerVerifiedFilled,
                  colorFilter: ColorFilter.mode(
                    const Color(0xFF7A7AEA),
                    BlendMode.srcIn,
                  ),
                  height: 125,
                ),
              ),
              const SizedBox(height: 32),
              TextPattern.customText(
                text:
                    'Olá!\n${(widget.controller.userName?.trim().isNotEmpty ?? false) ? widget.controller.userName : 'Promolover'}!',
                fontSize: 40,
                fontWeightOption: FontWeightOption.bold,
                textAlign: TextAlign.start,
                color: ColorOutlet.contentPrimary,
              ),

              const SizedBox(height: 32),
              TextPattern.customText(
                text:
                    'Sua conta foi criada, e nada nos deixa mais feliz!',
                fontSize: 24,
                fontWeightOption: FontWeightOption.bold,
                textAlign: TextAlign.start,
              ),
              const SizedBox(height: 32),
              TextPattern.customText(
                text:
                    'Complete seu perfil em dois passos e nos ajude a deixar tudo mais personalizado pra você.',
                fontSize: 16,
                fontWeightOption: FontWeightOption.regular,
                textAlign: TextAlign.start,
                color: ColorOutlet.contentGhost,
              ),
              Spacer(),
              NavigationButtonsRow(
                text: 'Completar perfil',
                onlyButton: true,
                onBack: () {},
                onNext: () {
                  widget.onNext();
                },
              ),
              SizedBox(height: paddingBottom),
            ],
          ),
        );
      },
    );
  }
}





// import 'package:flutter/material.dart';
// import 'package:promobell/src/modules/profile/controllers/profile_controller.dart';
// import 'package:promobell/src/modules/profile/ui/widgets/login/custom_text_input.dart';
// import 'package:promobell/src/modules/profile/ui/widgets/login/navigation_buttons_row.dart';
// import 'package:promobell/src/modules/profile/ui/widgets/login/onboarding_title_block.dart';
// import 'package:promobell/src/modules/profile/ui/widgets/login/page_view_template.dart';
// import 'package:promobell/src/modules/profile/ui/widgets/login/progress_badge.dart';

// class PageName extends StatefulWidget {
//   final VoidCallback onNext;
//   final ProfileController controller;

//   const PageName({required this.controller, required this.onNext, super.key});

//   @override
//   State<PageName> createState() => _PageNameState();
// }

// class _PageNameState extends State<PageName> {
//   late TextEditingController nameController;
//   late TextEditingController surnameController;
//   final nameFocusController = FocusNode();
//   final surnameFocusController = FocusNode();
//   final formKey = GlobalKey<FormState>();

//   void _updateControllerValues() {
//     widget.controller.setUserName(nameController.text, surnameController.text);
    
//   }

//   @override
//   void initState() {
//     nameController = TextEditingController(text: widget.controller.userName)..addListener(_updateControllerValues);
//     surnameController = TextEditingController(text: widget.controller.userSurname)..addListener(_updateControllerValues);
//     super.initState();
//   }

//   @override
//   void dispose() {
//     nameController.dispose();
//     surnameController.dispose();
//     nameFocusController.dispose();
//     surnameFocusController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final paddingBottom = MediaQuery.of(context).padding.bottom;
//     return AnimatedBuilder(
//       animation: Listenable.merge([nameController, surnameController, nameFocusController, surnameFocusController]),
//       builder: (context, _) {
//         return PageViewTemplate(
//           child: Column(
//             children: [
//               ProgressBadge(text: '1/3'),
//               OnboardingTitleBlock(
//                 title: 'Olá!\nComo você gostaria que a gente te chamasse?',
//                 subtitle: 'Isso nos ajuda a deixar tudo mais personalizado pra você.',
//               ),
//               SizedBox(height: 32),
//               Form(
//                 key: formKey,
//                 child: Column(
//                   spacing: 24,
//                   children: [
//                     CustomTextInput(
//                       controller: nameController,
//                       focusNode: nameFocusController,
//                       text: 'Nome',
//                       validator: widget.controller.validarCampoNome,
//                     ),
//                     CustomTextInput(
//                       focusNode: surnameFocusController,
//                       controller: surnameController,
//                       text: 'Sobrenome (opcional)',
//                       validator: widget.controller.validarCampoSobrenome,
//                     ),
//                   ],
//                 ),
//               ),
//               Spacer(),
//               NavigationButtonsRow(
//                 onlyButton: true,
//                 onBack: () {},
//                 onNext: () {
//                   if (formKey.currentState!.validate() && nameController.text.isNotEmpty) {
//                     widget.controller.setUserName(nameController.text, surnameController.text);
//                     widget.onNext();
//                   }
//                 },
//               ),
//               SizedBox(height: paddingBottom),
//             ],
//           ),
//         );
//       },
//     );
//   }
// }
