import 'dart:async';

import 'package:flutter/material.dart';

class StoryAnimationController extends ChangeNotifier {
  // MARK: - Propriedades
  static const double _dragsThreshold = 200.0;
  static const Duration _storyDuration = Duration(seconds: 6);
  bool _disposed = false;

  // Controle manual de tempo
  final Stopwatch _stopwatch = Stopwatch();
  Timer? _ticker;
  double progress = 0.0;
  final bool timerRunning = false;

  // Flags existentes
  bool _isPageFullyLoaded = false;
  final bool _isPlaying = true;
  bool _isLongPressed = false;
  bool _isTransitioning = false;
  double _headerOpacity = 1.0;
  double _dragStartY = 0.0;
  double _dragDistance = 0.0;
  bool _isDragging = false;

  // MARK: - Getters
  bool get isPlaying => _isPlaying;
  bool get isLongPressed => _isLongPressed;
  bool get isTransitioning => _isTransitioning;
  double get headerOpacity => _headerOpacity;
  double get dragDistance => _dragDistance;
  double get dragThreshold => _dragsThreshold;
  bool get isPageFullyLoaded => _isPageFullyLoaded;
  bool get isDragging => _isDragging;

  // MARK: - <PERSON><PERSON><PERSON> do timer manual
  void startAnimation() {
    _ticker?.cancel();
    _stopwatch
      ..reset()
      ..start();

    _ticker = Timer.periodic(const Duration(microseconds: 100), (_) {
      if (_disposed) return;
      if (!_stopwatch.isRunning) return;
      final elapsed = _stopwatch.elapsed;
      progress = (elapsed.inMilliseconds / _storyDuration.inMilliseconds).clamp(0.0, 1.0);
      notifyListeners();

      if (progress >= 1.0) {
        _ticker?.cancel();
        _stopwatch.stop();
        _checkComplete();
      }
    });
  }

  void pauseAnimation() {
    _stopwatch.stop();
    _isLongPressed = true;
    notifyListeners();
  }

  void resumeAnimation() {
    _stopwatch.start();
    _isLongPressed = false;
    notifyListeners();
  }

  void resetAnimation() {
    _ticker?.cancel();
    _stopwatch.reset();
    progress = 0.0;
    notifyListeners();
  }

  void _checkComplete() {
    notifyListeners();
  }

  // MARK: - Métodos de Drag e Long Press (mantidos iguais)
  void setLongPress(bool value) {
    _isLongPressed = value;
    if (!_isDragging) {
      _headerOpacity = value ? 0.0 : 1.0;
    }
    notifyListeners();
  }

  void setTransitioning(bool value) {
    _isTransitioning = value;
    notifyListeners();
  }

  void setHeaderOpacity(double value) {
    _headerOpacity = value;
    notifyListeners();
  }

  void handleDragStart(double startY) {
    _isDragging = true;
    _dragStartY = startY;
    _dragDistance = 0;
    _headerOpacity = 0.0;
    pauseAnimation();
    setLongPress(true);
  }

  void handleDragUpdate(double currentY) {
    _dragDistance = currentY - _dragStartY;
    notifyListeners();
  }

  void handleDragEnd() {
    _isDragging = false;
    if (_dragDistance > _dragsThreshold) {
      setLongPress(true);
    } else {
      _headerOpacity = 1.0;
      setLongPress(false);
      _dragDistance = 0;
      resumeAnimation();
    }
    notifyListeners();
  }

  void resetDrag() {
    _isDragging = false;
    _dragDistance = 0;
    _headerOpacity = 1.0;
    setLongPress(false);
  }

  // MARK: - Timeline / PageLoad (mantido)
  void initializeTimeline(TickerProvider vsync) {
    Future.delayed(const Duration(milliseconds: 350), () {
      if (_disposed) return;
      _isPageFullyLoaded = true;
      notifyListeners();
    });
  }

  void setPageLoadState(bool isLoaded) {
    _isPageFullyLoaded = isLoaded;
    notifyListeners();
  }

  @override
  void dispose() {
    _disposed = true;
    _ticker?.cancel();
    _stopwatch.stop();
    super.dispose();
  }
}
