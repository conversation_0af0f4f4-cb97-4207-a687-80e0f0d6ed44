import 'package:flutter/material.dart';
import '../../../../../components/shimmer_widget.dart';

class CardShimmer extends StatelessWidget {

  const CardShimmer({ super.key });


  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ShimmerWidget(height: 120, width: 120),
        const SizedBox(height: 8),
        Padding(
          padding: const EdgeInsets.only(left: 4),
          child: ShimmerWidget(height: 16, width: 88),
        ),
        const SizedBox(height: 8),
        Padding(
          padding: const EdgeInsets.only(left: 4),
          child: ShimmerWidget(height: 16, width: 48),
        ),
      ],
    );
  }
}
