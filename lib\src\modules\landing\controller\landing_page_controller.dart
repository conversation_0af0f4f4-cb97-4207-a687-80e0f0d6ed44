import 'dart:math';

import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class LandingPageController with ChangeNotifier {
  var polegadasTela = 0.0;
  var polegadasCalculada = false;

  calcularPolegadas(BuildContext context) {
    if (polegadasCalculada) return polegadasTela;
    polegadasCalculada = true;

    double larguraTela = MediaQuery.of(context).size.width;
    double alturaTela = MediaQuery.of(context).size.height;
    double diagonal = sqrt(pow(larguraTela, 2) + pow(alturaTela, 2));

    polegadasTela = diagonal / 96.0;
    polegadasCalculada = true;

    notifyListeners();
    return polegadasTela;
  }

  // Método para tratar os links do rodapé
  void handleFooterLink(String linkType) {
    switch (linkType) {
      case 'termos':
        launchURL('https://promobell.com.br/termos-de-uso');
        break;
      case 'privacidade':
        launchURL('https://promobell.com.br/politica-de-privacidade');
        break;
      case 'contato':
        launchURL('https://promobell.com.br/contato');
        break;
    }
  }

  void launchPlayStore() async {
    final Uri url = Uri.parse('https://play.google.com/store/apps/details?id=br.com.promobell.promobell');
    if (!await launchUrl(url)) {
      throw Exception('Não foi possível abrir a Play Store');
    }
  }

  void launchAppStore() async {
    final Uri url = Uri.parse('https://apps.apple.com/br/app/chegou-ofertas/id1234567890');
    if (!await launchUrl(url)) {
      throw Exception('Não foi possível abrir a App Store');
    }
  }

  void launchURL(String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url)) {
      throw Exception('Não foi possível abrir $urlString');
    }
  }

  void launchWhatsApp() async {
    final Uri url = Uri.parse('https://whatsapp.com/channel/0029Vb640YsLY6dEdm01ho2W');
    if (!await launchUrl(url)) {
      throw Exception('Não foi possível abrir o WhatsApp');
    }
  }

  void launchInstagram() async {
    final Uri url = Uri.parse('https://www.instagram.com/promobelloficial');
    if (!await launchUrl(url)) {
      throw Exception('Não foi possível abrir o WhatsApp');
    }
  }
}
