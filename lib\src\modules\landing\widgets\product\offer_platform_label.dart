import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:promobell_landing/src/components/text_pattern.dart';
import 'package:promobell_landing/src/theme/color_outlet.dart';
import 'package:promobell_landing/src/theme/svg_icons.dart';

class OfferPlatformLabel extends StatelessWidget {
  final String plataforma;

  const OfferPlatformLabel({required this.plataforma, super.key});

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(maxWidth: 326),
      child: Row(
        spacing: 8,
        children: [
          Container(
            height: 48,
            width: 48,
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(width: 0.5, color: ColorOutlet.systemBorderDisabled),
            ),
            child: SvgPicture.asset(iconeDaPlatafoma(plataforma)),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextPattern.customText(
                  text: 'Oferta',
                  color: ColorOutlet.contentGhost,
                  fontSize: 12,
                ),
                TextPattern.customText(
                  text: plataforma,
                  lineHeight: 1.5,
                  fontWeightOption: FontWeightOption.semiBold,
                  fontSize: 14,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

String iconeDaPlatafoma(String nome) {
  if (nome == 'Mercado Livre') {
    return SvgIcons.mercadoLivreLogoGrande;
  } else if (nome == 'Magazine Luiza') {
    SvgIcons.magaluLogoGrande;
  } else {
    return SvgIcons.amazonLogoGrande;
  }

  return '';
}
