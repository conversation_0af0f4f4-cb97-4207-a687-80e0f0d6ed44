# Implementação de Deferred Deep Links no Promobell

Este documento descreve a implementação de Deferred Deep Links (links diferidos) no aplicativo Promobell. Esta solução permite que você direcione usuários para conteúdo específico mesmo que eles não tenham o app instalado ainda - eles são redirecionados para a loja primeiro e depois para o conteúdo correto após a instalação.

## Visão Geral da Implementação

A implementação consiste em:

1. Uma tabela no Supabase para armazenar os links diferidos
2. Edge Functions do Supabase para gerenciar os links
3. Um serviço no Flutter para detectar o primeiro acesso e processar os links diferidos
4. Integração com o sistema de deep links existente

## Componentes da Solução

### 1. Tabela no Supabase

A tabela `deferred_links` armazena informações sobre os links diferidos:

```sql
CREATE TABLE deferred_links (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  device_id TEXT NOT NULL,
  target_path TEXT NOT NULL,
  params JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  accessed BOOLEAN DEFAULT FALSE,
  accessed_at TIMESTAMP WITH TIME ZONE
);
```

### 2. Edge Functions do Supabase

Duas Edge Functions foram criadas para gerenciar os links diferidos:

- `create-link`: Cria ou atualiza um link diferido para um dispositivo específico
- `get-link`: Recupera um link diferido para um dispositivo específico e o marca como acessado

### 3. Serviço Flutter

O serviço `DeferredLinkService` é responsável por:

- Gerar e armazenar um ID único para o dispositivo
- Verificar se é a primeira execução do app após a instalação
- Recuperar links diferidos pendentes
- Processar os links diferidos e navegar para o conteúdo correto

### 4. Integração com o AppLinksService

O serviço `AppLinksService` existente foi modificado para:

- Inicializar o serviço de links diferidos
- Verificar links diferidos na primeira execução
- Processar links diferidos e navegar para o conteúdo correto

## Configuração

### Supabase

1. Execute o script SQL para criar a tabela `deferred_links`:

```sql
-- Veja o arquivo supabase/migrations/create_deferred_links_table.sql
```

2. Implante as Edge Functions:

```bash
supabase functions deploy create-link
supabase functions deploy get-link
```

### Android

Adicione as seguintes configurações ao seu `AndroidManifest.xml`:

```xml
<activity
    android:name=".MainActivity"
    android:exported="true"
    android:launchMode="singleTask">
    <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
    </intent-filter>
    <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="https" />
        <data android:host="promobell.com.br" />
        <data android:pathPrefix="/link" />
    </intent-filter>
</activity>
```

### iOS

Adicione as seguintes configurações ao seu `Info.plist`:

```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleTypeRole</key>
        <string>Editor</string>
        <key>CFBundleURLName</key>
        <string>br.com.promobell.promobell</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>promobell</string>
        </array>
    </dict>
</array>
<key>FlutterDeepLinkingEnabled</key>
<true/>
<key>CFBundleVersion</key>
<string>$(FLUTTER_BUILD_NUMBER)</string>
<key>LSRequiresIPhoneOS</key>
<true/>
```

Adicione também a configuração de Associated Domains:

```xml
<key>com.apple.developer.associated-domains</key>
<array>
    <string>applinks:promobell.com.br</string>
</array>
```

### Configuração Web

1. Crie um arquivo `apple-app-site-association` na pasta `.well-known` do seu servidor web:

```json
{
  "applinks": {
    "apps": [],
    "details": [
      {
        "appID": "TEAM_ID.br.com.promobell.promobell",
        "paths": ["/link/*"]
      }
    ]
  }
}
```

2. Crie um arquivo `assetlinks.json` na pasta `.well-known` do seu servidor web:

```json
[{
  "relation": ["delegate_permission/common.handle_all_urls"],
  "target": {
    "namespace": "android_app",
    "package_name": "br.com.promobell.promobell",
    "sha256_cert_fingerprints": ["SEU_SHA256_FINGERPRINT"]
  }
}]
```

## Como Usar

### Criar um Link Diferido

Para criar um link diferido para um produto:

```dart
final deferredLinkService = DeferredLinkService();
final link = await deferredLinkService.createDeferredLink(
  targetPath: '/product',
  params: {'id': '123'},
);
```

Para criar um link diferido para uma categoria:

```dart
final deferredLinkService = DeferredLinkService();
final link = await deferredLinkService.createDeferredLink(
  targetPath: '/category',
  params: {'name': 'Beleza Pura'},
);
```

### Compartilhar um Link Diferido

Você pode compartilhar um link diferido usando a URL do seu site:

```
https://promobell.com.br/link?device_id=DEVICE_ID
```

Quando um usuário clicar neste link:

1. Se o app estiver instalado, ele será aberto diretamente no conteúdo específico
2. Se o app não estiver instalado, o usuário será redirecionado para a loja de aplicativos
3. Após instalar e abrir o app pela primeira vez, o usuário será automaticamente redirecionado para o conteúdo específico

## Considerações Finais

Esta implementação de Deferred Deep Links oferece várias vantagens:

- Controle total sobre os dados e o fluxo dos links diferidos
- Integração com a infraestrutura existente do Supabase
- Solução personalizada que atende às necessidades específicas do Promobell
- Não depende de serviços de terceiros desconhecidos

Para mais informações, consulte o código-fonte e os comentários nos arquivos relacionados.
