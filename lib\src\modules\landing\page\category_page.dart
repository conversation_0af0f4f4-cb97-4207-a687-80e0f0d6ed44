import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:meta_seo/meta_seo.dart';
import 'package:promobell_landing/src/components/text_pattern.dart';
import 'package:promobell_landing/src/models/categoria_menu.dart';
import 'package:promobell_landing/src/modules/landing/page/empty_state.dart';
import 'package:promobell_landing/src/modules/landing/widgets/lading-page/categories_section/categories_section.dart';
import 'package:promobell_landing/src/modules/landing/widgets/onboarding_download_section.dart';
import 'package:promobell_landing/src/services/supabase/categorias/get/get_categorias.dart';
import 'package:promobell_landing/src/theme/svg_icons.dart';

import '../../../components/no_mouse_drag_scroll_behavior.dart';
import '../../../components/seo/seo.dart';
import '../../../theme/color_outlet.dart';
import '../widgets/lading-page/landing_header/landing_header.dart';

class CategoryPage extends StatefulWidget {
  const CategoryPage({super.key});

  @override
  State<CategoryPage> createState() => _CategoryPageState();
}

class _CategoryPageState extends State<CategoryPage> implements Seo {
  CategoriaMenu? _categoria;
  bool _isLoading = true;
  String? _errorMessage;
  int _seguidoresCount = 0;
  int _produtosCount = 0;

  @override
  void initState() {
    super.initState();
    _loadCategoryData();
  }

  Future<void> _loadCategoryData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Obter o ID da categoria da URL
      final uri = Uri.parse(Uri.base.toString());
      final categoryIdStr = uri.queryParameters['id'];

      if (categoryIdStr == null || categoryIdStr.isEmpty) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'ID da categoria não fornecido';
        });
        return;
      }

      // Tentar converter o ID para inteiro
      final categoryId = int.tryParse(categoryIdStr);
      if (categoryId == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'ID da categoria inválido';
        });
        return;
      }

      // Primeiro, tentamos buscar a categoria do Supabase
      try {
        final getCategorias = Modular.get<GetCategorias>();
        final categoriaSupabase = await getCategorias.getCategoria(categoryId);

        if (categoriaSupabase != null) {
          // Buscar contagem de seguidores
          int seguidoresCount = 0;
          try {
            seguidoresCount = await getCategorias.getCategoryFollowersCount(categoryId);
          } catch (e) {
            debugPrint('Erro ao buscar seguidores: $e');
          }

          // Buscar contagem de produtos
          int produtosCount = 0;
          try {
            final getCategorias = Modular.get<GetCategorias>();
            produtosCount = await getCategorias.getTotalProdutosPorCategoria(categoriaSupabase.nome);
          } catch (e) {
            debugPrint('Erro ao buscar produtos: $e');
          }

          setState(() {
            _categoria = categoriaSupabase;
            _seguidoresCount = seguidoresCount;
            _produtosCount = produtosCount;
            _isLoading = false;
          });

          // Carregar as meta tags SEO
          loadSeo();
          return;
        }
      } catch (e) {
        // Se falhar, continuamos com o fallback local
        debugPrint('Erro ao buscar categoria do Supabase: $e');
      }

      // Fallback: Buscar a categoria localmente pelo ID
      final categoriaLocal = CategoriaMenu.getCategoriaById(categoryId);
      if (categoriaLocal == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Categoria não encontrada';
        });
        return;
      }

      setState(() {
        _categoria = categoriaLocal;
        _isLoading = false;
      });

      // Carregar as meta tags SEO
      loadSeo();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Erro ao carregar a categoria: $e';
      });
    }
  }

  // Formata o número para exibição
  String _formatNumber(int number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return number.toString();
    }
  }

  @override
  loadSeo() {
    if (_categoria == null) return;

    final meta = MetaSEO();
    meta.ogTitle(ogTitle: 'Categoria ${_categoria!.nome} - Promobell');
    meta.description(description: _categoria!.bio);
    meta.ogDescription(ogDescription: _categoria!.bio);

    // Usar a imagem da categoria
    String imageUrl;

    // Verificar se a imagem é uma URL completa (do Supabase) ou um caminho local
    if (_categoria!.fotoPequena.startsWith('http')) {
      // Já é uma URL completa
      imageUrl = _categoria!.fotoPequena;
    } else {
      // É um caminho local, converter para URL completa
      // Usamos o ID da categoria para construir a URL
      imageUrl =
          'https://promobell.com.br/assets/categorias/${_categoria!.nome.toLowerCase().replaceAll(' ', '_')}/category-image-${_categoria!.nome.toLowerCase().replaceAll(' ', '')}.png';
    }

    meta.ogImage(ogImage: imageUrl);

    meta.propertyContent(property: 'og:type', content: 'website');
    meta.propertyContent(property: 'og:url', content: 'https://promobell.com.br/category?id=${_categoria!.id}');
    meta.keywords(
      keywords:
          'ofertas, cupons de desconto, frete grátis, alertas de preço, produtos em promoção, marketplaces, Amazon, Magalu, Mercado Livre, app de economia, descontos online, notificações de promoções, rede social de ofertas, ${_categoria!.nome}, categorias personalizadas, buscar ofertas, economia inteligente, app de ofertas, alertas inteligentes, cupons exclusivos, o app alerta você economiza, promobell',
    );

    // Adicionar descrição adicional para melhorar o SEO
    meta.propertyContent(property: 'og:site_name', content: 'Promobell - O app alerta, você economiza');
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    if (_errorMessage != null || _categoria == null) {
      return EmptyState();
    }

    final categoriaCor = getFilteredColor(_categoria!.cor);

    return SelectionArea(
      child: Scaffold(
        backgroundColor: ColorOutlet.surface,
        body: ScrollConfiguration(
          behavior: NoMouseDragScrollBehavior(),
          child: Stack(
            children: [
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(height: 96, color: Color(0xFFFAFAFC).withValues(alpha: 80)),
              ),
              CustomScrollView(
                slivers: [
                  const LandingHeader(center: true),
                  SliverToBoxAdapter(
                    child: Column(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              stops: [0.0, 0.37, 0.37, 1.0],
                              colors: [categoriaCor, categoriaCor, ColorOutlet.paper, ColorOutlet.paper],
                            ),
                          ),
                          child: FixedWidthFittedSection(
                            width: 390,
                            child: Stack(
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SizedBox(height: 72),
                                    Container(
                                      height: 240,
                                      width: 390,
                                      decoration: BoxDecoration(
                                        color: ColorOutlet.paper,
                                        borderRadius: BorderRadius.circular(24),
                                      ),
                                      child: Column(
                                        children: [
                                          SizedBox(height: 56),
                                          Row(
                                            spacing: 8,
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              TextPattern.customText(
                                                text: _categoria!.nome,
                                                fontSize: 20,
                                                fontWeightOption: FontWeightOption.bold,
                                              ),
                                              SvgPicture.asset(
                                                SvgIcons.markerVerifiedFilled,
                                                colorFilter: ColorFilter.mode(
                                                  ColorOutlet.contentPrimary,
                                                  BlendMode.srcIn,
                                                ),
                                              ),
                                            ],
                                          ),
                                          Spacer(),
                                          ConstrainedBox(
                                            constraints: BoxConstraints(maxWidth: 310),
                                            child: TextPattern.customText(
                                              text: _categoria!.bio,
                                              fontSize: 14,
                                              textAlign: TextAlign.center,
                                              maxLines: 3,
                                            ),
                                          ),
                                          Spacer(),
                                          Row(
                                            children: [
                                              Spacer(),
                                              FollowersOrOffersColumn(
                                                title: _formatNumber(_seguidoresCount),
                                                subtitle: 'Seguidores',
                                              ),
                                              SizedBox(width: 8),
                                              FollowersOrOffersColumn(
                                                title: _formatNumber(_produtosCount),
                                                subtitle: 'Ofertas',
                                              ),
                                              Spacer(),
                                            ],
                                          ),
                                          SizedBox(height: 32),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                Align(
                                  alignment: Alignment.topCenter,
                                  child: Padding(
                                    padding: const EdgeInsets.only(top: 32),
                                    child: Container(
                                      height: 80,
                                      width: 80,
                                      padding: EdgeInsets.only(top: 32, left: 32),
                                      decoration: BoxDecoration(
                                        color: categoriaCor,
                                        borderRadius: BorderRadius.circular(24),
                                        border: Border.all(color: ColorOutlet.paper, width: 4),
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.only(bottomRight: Radius.circular(20)),
                                        child: Image.asset(_categoria!.fotoPequena, width: 48, height: 48),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        OnboardingDownloadSection(),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class FollowersOrOffersColumn extends StatelessWidget {
  final String title;
  final String subtitle;

  const FollowersOrOffersColumn({required this.title, required this.subtitle, super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 151,
      child: Column(
        children: [
          TextPattern.customText(
            text: title,
            fontSize: 16,
            color: ColorOutlet.contentPrimary,
            fontWeightOption: FontWeightOption.semiBold,
          ),
          TextPattern.customText(text: subtitle, fontSize: 12),
        ],
      ),
    );
  }
}
