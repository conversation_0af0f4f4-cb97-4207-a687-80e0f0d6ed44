import 'package:flutter/material.dart';

import '../../../../../components/responsive/responsive_padding_wrappe.dart';
import '../../../../../theme/color_outlet.dart';
import 'channel_header_mockup.dart';
import 'whats_app_promo_section_web.dart';

class WhatsAppSectionMobile extends StatelessWidget {
  const WhatsAppSectionMobile({super.key, required this.onWhatsAppButtonPressed});

  final Function() onWhatsAppButtonPressed;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.bottomCenter,
          end: Alignment.topCenter,
          stops: [0.0, 0.56, 0.56, 1.0],
          colors: [ColorOutlet.contentPrimary, ColorOutlet.contentPrimary, Colors.transparent, Colors.transparent],
        ),
      ),
      child: ResponsivePaddingWrapper.mobile(
        maxWidth: 512.0,
        minPadding: 16,
        child: FittedBox(
          fit: BoxFit.cover,
          child: SizedBox(
            height: 1071,
            child: Stack(
              alignment: Alignment.topCenter,
              clipBehavior: Clip.none,
              children: [
                Column(
                  children: [
                    Container(
                      height: 903,
                      padding: const EdgeInsets.only(top: 56, left: 56, right: 56),
                      decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(24)),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [ColorOutlet.paper, Color(0xFFF2EADF)],
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          WhatsAppPromoSection(onWhatsAppButtonPressed: onWhatsAppButtonPressed),
                          const SizedBox(height: 56),
                          const ChannelHeaderMockup(),
                        ],
                      ),
                    ),
                    SizedBox(height: 87),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
