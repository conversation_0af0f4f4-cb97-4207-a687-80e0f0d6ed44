import '../../../../../components/text_pattern.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';

class ShimmerBuscar extends StatelessWidget {
  const ShimmerBuscar({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 32, top: 16),
      child: Container(
        color: ColorOutlet.surface,
        height: 48,
        padding: EdgeInsets.symmetric(horizontal: 16),
        child: TextField(
          onTapOutside: (event) => FocusScope.of(context).unfocus(),
          decoration: InputDecoration(
            contentPadding: EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
            hintText: 'Encontre produtos, marcas e muito mais...',
            prefixIcon: Container(
              height: 24,
              width: 24,
              padding: const EdgeInsets.only(left: 12, right: 8),
              child: SvgPicture.asset(
                SvgIcons.actionSearch,
                colorFilter: ColorFilter.mode(
                  ColorOutlet.contentSecondary,
                  BlendMode.srcIn,
                ),
                height: 14,
                width: 14,
              ),
            ),
            hintStyle: TextStyle(
              color: ColorOutlet.contentGhost,
              fontSize: 14,
              fontFamily: TextPattern().fontFamily,
              fontWeight: FontWeight.w400,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide.none,
            ),
            filled: true,
            fillColor: ColorOutlet.paper,
          ),
          cursorColor: ColorOutlet.contentGhost,
          style: TextStyle(
            color: ColorOutlet.contentGhost,
            fontSize: 14,
          ),
        ),
      ),
    );
  }
}
