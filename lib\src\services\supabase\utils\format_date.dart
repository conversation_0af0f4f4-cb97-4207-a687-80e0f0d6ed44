import 'package:intl/intl.dart';

import '../../logs/app_logger.dart';

String formatJoinedDate(String? dateString) {
  if (dateString == null || dateString.isEmpty) {
    return '';
  }

  try {
    final date = DateTime.tryParse(dateString);
    if (date != null) {
      return DateFormat('dd/MM/yyyy').format(date);
    }
    return '';
  } catch (e) {
    AppLogger.logError('Erro ao formatar data', e, StackTrace.current);
    return '';
  }
}
