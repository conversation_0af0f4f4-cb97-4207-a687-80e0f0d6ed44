import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../../../theme/svg_icons.dart';
import '../../../../../core/base/controllers/base_controller.dart/base_controller.dart';
import '../../../../../models/categorias_menu.dart';
import '../../../../offers/controllers/offers_controller.dart';
import '../../../controllers/categories_controller.dart';
import 'app_bar_animations.dart';
import 'button_icon_with_background.dart';

class CategoryHeaderWithActions extends StatefulWidget {
  final double heightAppBar;
  final CategoriesController controller;
  final OffersController offersController;
  final CategoriaMenu category;

  const CategoryHeaderWithActions({
    super.key,
    required this.heightAppBar,
    required this.controller,
    required this.category,
    required this.offersController,
  });

  @override
  State<CategoryHeaderWithActions> createState() =>
      _CategoryHeaderWithActionsState();
}

class _CategoryHeaderWithActionsState
    extends State<CategoryHeaderWithActions> {
  final baseController = Modular.get<BaseController>();
  late bool _isFromDeepLink;

  @override
  void initState() {
    super.initState();
    // Verifica se a tela foi aberta por um deeplink
    // Consideramos que é um deeplink se a rota atual não for '/home'
    _isFromDeepLink = Modular.to.path != '/home';
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: widget.controller.topPadding),
        SizedBox(
          height: 56,
          child: Align(
            alignment: Alignment.topCenter,
            child: Padding(
              padding: EdgeInsets.only(left: 16, right: 16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ButtonIconWithBackground(
                    iconPath: SvgIcons.arrowClearLeft,
                    onPressed: () {
                      if (_isFromDeepLink) {
                        // Se veio de um deeplink, usa o método especial para voltar para a tela de categorias
                        baseController.navBackFromCategoryDeepLink();
                      } else {
                        // Comportamento normal
                        Modular.to.pop();
                      }
                      widget.offersController.limparTodosOsFiltros(
                        isCategoryFilters: false,
                      );
                      widget.offersController.setFilterCategory(
                        "Recentes",
                      );
                    },
                  ),
                  Expanded(
                    child: Container(
                      height: 40,
                      alignment: Alignment.centerLeft,
                      child: AppBarTitleAnimations(
                        controller: widget.controller,
                        category: widget.category,
                      ),
                    ),
                  ),
                  SizedBox(width: 16),
                  AnimatedActionsRow(
                    controller: widget.controller,
                    category: widget.category,
                    offersController: widget.offersController,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
