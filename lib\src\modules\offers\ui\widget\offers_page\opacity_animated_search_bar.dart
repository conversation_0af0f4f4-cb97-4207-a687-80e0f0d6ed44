import 'package:flutter/material.dart';
import 'package:promobell/src/modules/offers/ui/widget/offers_page/custom_campo_buscar.dart';

class OpacityAnimatedSearchBar extends StatelessWidget {
  final double progress;
  final bool isFixedFilter;

  const OpacityAnimatedSearchBar({
    required this.progress,
    required this.isFixedFilter,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    // Se progress for 1.0, estamos em modo de busca, então não aplicamos animação
    final bool isSearchMode = progress >= 1.0;

    if (isSearchMode) {
      return CampoBuscar(isFixedFilter: isFixedFilter);
    }

    final clamped = progress.clamp(0.7, 1.0);
    final opacity = (clamped - 0.7) / 0.3;
    final translateY = -30 * (1 - opacity);

    return IgnorePointer(
      ignoring: opacity == 0.0,
      child: Transform.translate(
        offset: Offset(0, translateY),
        child: <PERSON><PERSON>us<PERSON>(isFixedFilter: isFixedFilter),
      ),
    );
  }
}
