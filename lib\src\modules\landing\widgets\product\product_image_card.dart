import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:promobell_landing/src/theme/color_outlet.dart';
import 'package:promobell_landing/src/theme/svg_icons.dart';

class ProductImageCard extends StatelessWidget {
  final String urlImage;

  const ProductImageCard({required this.urlImage, super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 216,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: ColorOutlet.surface,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: CachedNetworkImage(
          imageUrl: urlImage,
          height: 216,
          width: 326,
          fit: BoxFit.contain,
          color: ColorOutlet.surface,
          colorBlendMode: BlendMode.multiply,
          progressIndicatorBuilder:
              (context, url, progress) => Center(
                child: CircularProgressIndicator(
                  value: progress.progress,
                  color: ColorOutlet.contentPrimary,
                  strokeWidth: 1.5,
                ),
              ),
          errorWidget:
              (context, url, error) => Center(
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: FittedBox(
                    child: SvgPicture.asset(SvgIcons.archiveImageBroken, fit: BoxFit.cover),
                  ),
                ),
              ),
        ),
      ),
    );
  }
}
