import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../theme/color_outlet.dart';
import '../../../../../theme/svg_icons.dart';

class PromobellAvatar extends StatelessWidget {
  const PromobellAvatar({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 38,
      width: 38,
      padding: EdgeInsets.all(8),
      decoration: BoxDecoration(color: ColorOutlet.contentPrimary, borderRadius: BorderRadius.circular(80)),
      child: SvgPicture.asset(
        SvgIcons.logoPromobell,
        colorFilter: ColorFilter.mode(ColorOutlet.contentTertiary, BlendMode.srcIn),
      ),
    );
  }
}
