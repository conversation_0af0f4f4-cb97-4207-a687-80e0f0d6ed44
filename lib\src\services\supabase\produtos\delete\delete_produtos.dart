import '../../../logs/app_logger.dart';
import '../../db/db.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class DeleteProdutos {
  final SupabaseClient _supabase = Supabase.instance.client;
  DB db = DB();

  Future<void> unlikeProduct(int productId, String userEmail) async {
    try {
      await _supabase.from(db.tabelaDeProdutosLikes).delete().match(
        {
          'product_id': productId,
          'user_email': userEmail,
        },
      );
    } catch (e) {
      AppLogger.logError('Erro ao descurtir o produto', e, StackTrace.current);
    }
  }
}
