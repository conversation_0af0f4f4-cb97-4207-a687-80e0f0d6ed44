                        -HH:\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-D<PERSON>DROID_ABI=x86_64
-DC<PERSON>KE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=H:\Android\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=H:\Android\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=H:\Android\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=H:\Android\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=H:\projetos\chegou mercado\promobell\build\app\intermediates\cxx\Debug\d2s552k2\obj\x86_64
-DC<PERSON><PERSON>_RUNTIME_OUTPUT_DIRECTORY=H:\projetos\chegou mercado\promobell\build\app\intermediates\cxx\Debug\d2s552k2\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-BH:\projetos\chegou mercado\promobell\android\app\.cxx\Debug\d2s552k2\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2