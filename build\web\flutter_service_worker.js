'use strict';
const MANIFEST = 'flutter-app-manifest';
const TEMP = 'flutter-temp-cache';
const CACHE_NAME = 'flutter-app-cache';

const RESOURCES = {".well-known/apple-app-site-association": "2846f1c22593c8680c90c3892e0249f6",
".well-known/assetlinks.json": "82d0d8ad7fa5df85993d1183c61eb2b4",
"assets/AssetManifest.bin": "6daae68a014b459270071c5ddd8bf9d8",
"assets/AssetManifest.bin.json": "483acacb06f60263e7daa7f76b771dfd",
"assets/AssetManifest.json": "e1d3ba09ec2b83749f7f0d5bf4ea5bec",
"assets/assets/categorias/beleza_pura/category-image-belezapura.png": "2903564acd42e1ef5eb90a9384ba5b8d",
"assets/assets/categorias/beleza_pura/category-video-belezapura-720p.mp4": "7d7a81f36d05e6de00d9b995afead49e",
"assets/assets/categorias/beleza_pura/cuidado_pessoal.png": "502112cf550da83a4a5d8896a415fec1",
"assets/assets/categorias/brinde_perfeito/brinde-perfeito.mp4": "8ffb5c8a311cbe0974c37985288e0f8c",
"assets/assets/categorias/brinde_perfeito/category-image-habitosdiarios.png": "4b5b68012cf1896db1f099c108e21aef",
"assets/assets/categorias/eletro_lar/category-image-eletroelar.png": "c68b7607b5c5846aff26250166a57c0b",
"assets/assets/categorias/eletro_lar/category-video-eletrolar-720p.mp4": "1cdd1e5c1039120414bd84dc8e8e08f3",
"assets/assets/categorias/eletro_lar/para_casa.jpg": "a544349e8bcb31c12e743da2fc0c1e3a",
"assets/assets/categorias/espaco_decor/category-image-espacobelo.png": "52571e7292c47cdeb2f8db078651eb4d",
"assets/assets/categorias/espaco_decor/espaco-decor.mp4": "6082190129bc7c2e7c1a1708cfbf67e2",
"assets/assets/categorias/mais_saude/category-image-maissaude.png": "26cfb9576dfb85e3f522fcc0c78dbc6b",
"assets/assets/categorias/mais_saude/category-video-maissaude-720p.mp4": "3a5669edbf4fb5f670fa930c3dd67a5e",
"assets/assets/categorias/melhor_amigo/category-image-melhoramigo.png": "22dd80fe7754fa12a20918aa858161e7",
"assets/assets/categorias/melhor_amigo/category-video-melhoramigo-720p.mp4": "28f4eabb87da04c2ca4bc00b37ecc277",
"assets/assets/categorias/melhor_estilo/category-image-melhorestilo.png": "a2c7ec74e6c8408d531257db93cd5289",
"assets/assets/categorias/melhor_estilo/category-video-melhorestilo-720p.mp4": "ef303b764ae3797324b6f3cfc2722d0c",
"assets/assets/categorias/melhor_estilo/melhor_estilo.png": "7a16fab1a46d56019d6133320e966a49",
"assets/assets/categorias/mundo_baby/category-image-probaby.png": "155f53631672142cbbce0068d8466756",
"assets/assets/categorias/mundo_baby/mundo-baby.mp4": "b275b8453870ffb48a7dbacb7d8b9b8b",
"assets/assets/categorias/nutri_day/category-image-nutriday.png": "117809bc95290570bf4e18852e5be78f",
"assets/assets/categorias/nutri_day/category-video-nutriday-720p.mp4": "ffb0d0aedb3531243bb15f07e530f959",
"assets/assets/categorias/oficina_mix/category-image-ofcinashow.png": "5c6256506067b93239216e3629182956",
"assets/assets/categorias/oficina_mix/oficina-mix.mp4": "cf5e118586655baf0698957f499904aa",
"assets/assets/categorias/sobre_rodas/category-image-sobrerodas.png": "63e5698f484db183f60e3c03360948c6",
"assets/assets/categorias/sobre_rodas/category-video-sobrerodas-720p.mp4": "d5ee0d7bf71387e4d7f8280af15a85d6",
"assets/assets/categorias/tempo_livre/category-image-tempolivre.png": "bc3f4bc86cb00b09d5f87be4dacae081",
"assets/assets/categorias/tempo_livre/tempo-livre.mp4": "2136f043b2fde4c8d8651e438aaf9ce5",
"assets/assets/categorias/tudo_tech/category-image-tudotech.png": "ed11b92f4944cefde40f921079eff405",
"assets/assets/categorias/tudo_tech/category-video-tudotech-720p.mp4": "02e693b1a404db4f5cf67d8d29a5723b",
"assets/assets/categorias/tudo_tech/mobile_small.webp": "5702738b045a95e413abbc13b03a2ef2",
"assets/assets/categorias/vibe_mobile/category-image-vibemobile.png": "012b5b9f1a74b3396dc700c6504a1220",
"assets/assets/categorias/vibe_mobile/vibe-mobile.mp4": "41b93cac622096c6eaa4fa4d5e930a7d",
"assets/assets/categorias/vida_ativa/category-image-vidaativa.png": "18adc5477a0a99f6893344e3db57cb51",
"assets/assets/categorias/vida_ativa/category-video-vidaativa-720p.mp4": "d0dda145cdc7aea783cfdf69be416acb",
"assets/assets/fonts/Figtree-Bold.ttf": "aae832abcd32ce810f204801f1f7414b",
"assets/assets/fonts/Figtree-Medium.ttf": "333d515de89c768ce65d1bfe8bb78091",
"assets/assets/fonts/Figtree-Regular.ttf": "5f1dc6017b44a0872c44171c5b2fb589",
"assets/assets/fonts/Figtree-SemiBold.ttf": "689226050168b640b1e67e0361904536",
"assets/assets/fonts/Roboto-Bold.ttf": "8c9110ec6a1737b15a5611dc810b0f92",
"assets/assets/fonts/Roboto-Medium.ttf": "7d752fb726f5ece291e2e522fcecf86d",
"assets/assets/fonts/Roboto-Regular.ttf": "303c6d9e16168364d3bc5b7f766cfff4",
"assets/assets/fonts/Roboto-SemiBold.ttf": "dae3c6eddbf79c41f922e4809ca9d09c",
"assets/assets/icons/%25E2%259D%25A4%25EF%25B8%258F.png": "b01010375a98b631572a43a7872bd90f",
"assets/assets/icons/%25F0%259F%2591%258D.png": "9cdd772e433b7655f36e523df1a31af8",
"assets/assets/icons/%25F0%259F%2594%25A5.png": "d3a014b6edff2f7239c787403e7e6446",
"assets/assets/icons/%25F0%259F%2598%258D.png": "567e9102829a182007643d786f578c3f",
"assets/assets/icons/action-category.svg": "1f638e7b09a250890d26776b486181e2",
"assets/assets/icons/action-notification.svg": "68f24b3b8ae011e78f6a66025fb50d19",
"assets/assets/icons/arrow-link.svg": "f340d8d3fd1647b60ac5a8ebd43171e7",
"assets/assets/icons/bookmark.svg": "db625f85fe292889aff9d39e1349f45c",
"assets/assets/icons/coracao.svg": "7d86888f4e0316c4a328c3a93ef279c7",
"assets/assets/icons/foguinho.svg": "f8c703e24fe01687aeeda20aab765ec5",
"assets/assets/icons/icon-arrow-tip-right.svg": "ec1486491da4bcc9f18c302aa2994720",
"assets/assets/icons/image-broken.svg": "0d31381b97b17808a1a45fb9a93ea71f",
"assets/assets/icons/Instagram.svg": "5676107505a73fbcf4a5d7b201563f74",
"assets/assets/icons/joinha.svg": "186325e88b5d2b76aec83cbef8b591c8",
"assets/assets/icons/marker-favorite.svg": "d0713f023b11563e1029368be9943241",
"assets/assets/icons/marker-shipping.svg": "f07f3839cf8948351a12f42514b270ea",
"assets/assets/icons/marker-verified-filled.svg": "ca6bb9bb5525099261ec102456c28428",
"assets/assets/icons/marker-verified.svg": "9ddd59d8f2406dbd76f627e47bea643e",
"assets/assets/icons/notification-broken.svg": "820443ddeca4ea9f165b7251f73338c9",
"assets/assets/icons/offer-discount-filled.svg": "a3f0d2cc3519ed1a81144ca35e5bb69a",
"assets/assets/icons/whatsapp.svg": "31663a7d826ee5ce935233af62de9389",
"assets/assets/images/bookmark.png": "ed92f6bb4a2eb755bc20511b15bb84c0",
"assets/assets/images/category-image-eletroelar.png": "7fa79a987cf8e317e0442d4e16e232cf",
"assets/assets/images/category-image-espacobelo.png": "d44aa5f65bfbd1f43590b9a44b42cb8a",
"assets/assets/images/category-image-tudotech.png": "58769d00d39d3f540a7b2c0adb4021f2",
"assets/assets/images/category-image-vibemobile.png": "4332377bcc443de4cb51b81c6be456e1",
"assets/assets/images/conexao-segura-svg.svg": "7d5574056abaf86d7920f5fa77930456",
"assets/assets/images/delete-account-001.png": "69747d7fe62e4b52b07a9c349fb8e1f2",
"assets/assets/images/delete-account-002.png": "deaf1200bfb4059000044613e4d596d9",
"assets/assets/images/delete-account-003.png": "f2aeeb4dc15a51d509becbf80f553439",
"assets/assets/images/favorite.png": "35ed8a43184b0068a8f263567a6055da",
"assets/assets/images/frame.png": "776ad2d1065b9720a26ea4543f17b2d5",
"assets/assets/images/fritadeira.png": "088de5f93f3f67763d48fd8bffc700da",
"assets/assets/images/home.png": "855316ebe56f14626a860d0f270ae2fd",
"assets/assets/images/offer-card-direita.png": "8c40824e1565b2b9f763d0cd23910922",
"assets/assets/images/offer-card-esquerda.png": "dcfb8d551006d4c4b04368ee5d10a34c",
"assets/assets/images/phone.png": "b8ff0d7026c46f67e87ad20cc263096f",
"assets/assets/images/promobell.svg": "094170b6bd14ce15df9ae80bdf593c70",
"assets/assets/images/share.png": "303f6f228943bb881d20b6a38240aabe",
"assets/assets/images/videoframe.png": "57bf9c9fab768a0e9ed861e185e52d7a",
"assets/assets/logos/amazon-logo-grande.svg": "3598567ae9bc9834f1d38fdf0545271b",
"assets/assets/logos/apple.svg": "3bc5656b17a1e557d587ec955c261341",
"assets/assets/logos/avatar.svg": "80c9f23c6037ec608ea114d57e45f9d8",
"assets/assets/logos/magalu-logo-grande.svg": "012e22975ab26c870170984ac4189f12",
"assets/assets/logos/mercado-livre-logo-grande.svg": "4d432f06bd827db856d7bbddd51ad982",
"assets/assets/logos/play-store.svg": "a800fcc7eabb08942178b83c470797a4",
"assets/assets/lottie/pesquisar-fina.json": "6ae75ed77cf0d0b90671086a55babe34",
"assets/assets/video/espaco-decor.mp4": "6082190129bc7c2e7c1a1708cfbf67e2",
"assets/FontManifest.json": "eba2ea5b54651cd0c435823cc5d89c9c",
"assets/fonts/MaterialIcons-Regular.otf": "603383c66fbdbdcb1f124ccfe8829062",
"assets/NOTICES": "9b513c960994aca963f5205da7742d03",
"assets/packages/cupertino_icons/assets/CupertinoIcons.ttf": "33b7d9392238c04c131b6ce224e13711",
"assets/shaders/ink_sparkle.frag": "ecc85a2e95f5e9f53123dcaf8cb9b6ce",
"canvaskit/canvaskit.js": "86e461cf471c1640fd2b461ece4589df",
"canvaskit/canvaskit.js.symbols": "68eb703b9a609baef8ee0e413b442f33",
"canvaskit/canvaskit.wasm": "efeeba7dcc952dae57870d4df3111fad",
"canvaskit/chromium/canvaskit.js": "34beda9f39eb7d992d46125ca868dc61",
"canvaskit/chromium/canvaskit.js.symbols": "5a23598a2a8efd18ec3b60de5d28af8f",
"canvaskit/chromium/canvaskit.wasm": "64a386c87532ae52ae041d18a32a3635",
"canvaskit/skwasm.js": "f2ad9363618c5f62e813740099a80e63",
"canvaskit/skwasm.js.symbols": "80806576fa1056b43dd6d0b445b4b6f7",
"canvaskit/skwasm.wasm": "f0dfd99007f989368db17c9abeed5a49",
"canvaskit/skwasm_st.js": "d1326ceef381ad382ab492ba5d96f04d",
"canvaskit/skwasm_st.js.symbols": "c7e7aac7cd8b612defd62b43e3050bdd",
"canvaskit/skwasm_st.wasm": "56c3973560dfcbf28ce47cebe40f3206",
"favicon.png": "cb4472d5fb7e65c07167c9ec1d55c08d",
"flutter.js": "76f08d47ff9f5715220992f993002504",
"flutter_bootstrap.js": "d218d600738cef1a7f568ea894760d78",
"index.html": "efb3c307035832d9d98b8882fefa273e",
"/": "efb3c307035832d9d98b8882fefa273e",
"main.dart.js": "29cc71a748ce4a31c01a6cdce42bbcc1",
"manifest.json": "5b3c83aa7f2a37c81f5f10240c1fd37e",
"promobell-icon-192.png": "a2add1d7a59d075d175f0ad677b23155",
"promobell-icon-512.png": "fc6a0895be1624f533dff1ac2f357b17",
"README.md": "e89ed8f2f46f66d8f70c5c972ccff3f0",
"robots.txt": "75d26e6957fc553f576b17e19ce7e8d2",
"seo_helper.js": "cc2f3cf0cdbeda476cacbeca79ca4675",
"sitemap.xml": "3392f415675d061da2af27928e6a09c9",
"version.json": "018f0095775fb6f4b4cc5750fa34226d"};
// The application shell files that are downloaded before a service worker can
// start.
const CORE = ["main.dart.js",
"index.html",
"flutter_bootstrap.js",
"assets/AssetManifest.bin.json",
"assets/FontManifest.json"];

// During install, the TEMP cache is populated with the application shell files.
self.addEventListener("install", (event) => {
  self.skipWaiting();
  return event.waitUntil(
    caches.open(TEMP).then((cache) => {
      return cache.addAll(
        CORE.map((value) => new Request(value, {'cache': 'reload'})));
    })
  );
});
// During activate, the cache is populated with the temp files downloaded in
// install. If this service worker is upgrading from one with a saved
// MANIFEST, then use this to retain unchanged resource files.
self.addEventListener("activate", function(event) {
  return event.waitUntil(async function() {
    try {
      var contentCache = await caches.open(CACHE_NAME);
      var tempCache = await caches.open(TEMP);
      var manifestCache = await caches.open(MANIFEST);
      var manifest = await manifestCache.match('manifest');
      // When there is no prior manifest, clear the entire cache.
      if (!manifest) {
        await caches.delete(CACHE_NAME);
        contentCache = await caches.open(CACHE_NAME);
        for (var request of await tempCache.keys()) {
          var response = await tempCache.match(request);
          await contentCache.put(request, response);
        }
        await caches.delete(TEMP);
        // Save the manifest to make future upgrades efficient.
        await manifestCache.put('manifest', new Response(JSON.stringify(RESOURCES)));
        // Claim client to enable caching on first launch
        self.clients.claim();
        return;
      }
      var oldManifest = await manifest.json();
      var origin = self.location.origin;
      for (var request of await contentCache.keys()) {
        var key = request.url.substring(origin.length + 1);
        if (key == "") {
          key = "/";
        }
        // If a resource from the old manifest is not in the new cache, or if
        // the MD5 sum has changed, delete it. Otherwise the resource is left
        // in the cache and can be reused by the new service worker.
        if (!RESOURCES[key] || RESOURCES[key] != oldManifest[key]) {
          await contentCache.delete(request);
        }
      }
      // Populate the cache with the app shell TEMP files, potentially overwriting
      // cache files preserved above.
      for (var request of await tempCache.keys()) {
        var response = await tempCache.match(request);
        await contentCache.put(request, response);
      }
      await caches.delete(TEMP);
      // Save the manifest to make future upgrades efficient.
      await manifestCache.put('manifest', new Response(JSON.stringify(RESOURCES)));
      // Claim client to enable caching on first launch
      self.clients.claim();
      return;
    } catch (err) {
      // On an unhandled exception the state of the cache cannot be guaranteed.
      console.error('Failed to upgrade service worker: ' + err);
      await caches.delete(CACHE_NAME);
      await caches.delete(TEMP);
      await caches.delete(MANIFEST);
    }
  }());
});
// The fetch handler redirects requests for RESOURCE files to the service
// worker cache.
self.addEventListener("fetch", (event) => {
  if (event.request.method !== 'GET') {
    return;
  }
  var origin = self.location.origin;
  var key = event.request.url.substring(origin.length + 1);
  // Redirect URLs to the index.html
  if (key.indexOf('?v=') != -1) {
    key = key.split('?v=')[0];
  }
  if (event.request.url == origin || event.request.url.startsWith(origin + '/#') || key == '') {
    key = '/';
  }
  // If the URL is not the RESOURCE list then return to signal that the
  // browser should take over.
  if (!RESOURCES[key]) {
    return;
  }
  // If the URL is the index.html, perform an online-first request.
  if (key == '/') {
    return onlineFirst(event);
  }
  event.respondWith(caches.open(CACHE_NAME)
    .then((cache) =>  {
      return cache.match(event.request).then((response) => {
        // Either respond with the cached resource, or perform a fetch and
        // lazily populate the cache only if the resource was successfully fetched.
        return response || fetch(event.request).then((response) => {
          if (response && Boolean(response.ok)) {
            cache.put(event.request, response.clone());
          }
          return response;
        });
      })
    })
  );
});
self.addEventListener('message', (event) => {
  // SkipWaiting can be used to immediately activate a waiting service worker.
  // This will also require a page refresh triggered by the main worker.
  if (event.data === 'skipWaiting') {
    self.skipWaiting();
    return;
  }
  if (event.data === 'downloadOffline') {
    downloadOffline();
    return;
  }
});
// Download offline will check the RESOURCES for all files not in the cache
// and populate them.
async function downloadOffline() {
  var resources = [];
  var contentCache = await caches.open(CACHE_NAME);
  var currentContent = {};
  for (var request of await contentCache.keys()) {
    var key = request.url.substring(origin.length + 1);
    if (key == "") {
      key = "/";
    }
    currentContent[key] = true;
  }
  for (var resourceKey of Object.keys(RESOURCES)) {
    if (!currentContent[resourceKey]) {
      resources.push(resourceKey);
    }
  }
  return contentCache.addAll(resources);
}
// Attempt to download the resource online before falling back to
// the offline cache.
function onlineFirst(event) {
  return event.respondWith(
    fetch(event.request).then((response) => {
      return caches.open(CACHE_NAME).then((cache) => {
        cache.put(event.request, response.clone());
        return response;
      });
    }).catch((error) => {
      return caches.open(CACHE_NAME).then((cache) => {
        return cache.match(event.request).then((response) => {
          if (response != null) {
            return response;
          }
          throw error;
        });
      });
    })
  );
}
