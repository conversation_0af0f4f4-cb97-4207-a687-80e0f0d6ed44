import 'dart:convert';

class NotificationStorageService {
  final int? id;
  final String? title;
  final String? body;
  final String? date;
  int? isRead;

  NotificationStorageService({
    this.id,
    this.title,
    this.body,
    this.date,
    this.isRead,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'title': title,
      'body': body,
      'date': date,
      'isRead': isRead,
    };
  }

  factory NotificationStorageService.fromMap(Map<String, dynamic> map) {
    return NotificationStorageService(
      id: map['id'] != null ? map['id'] as int : null,
      title: map['title'] != null ? map['title'] as String : null,
      body: map['body'] != null ? map['body'] as String : null,
      date: map['date'] != null ? map['date'] as String : null,
      isRead: map['isRead'] != null ? map['isRead'] as int : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory NotificationStorageService.fromJson(String source) => NotificationStorageService.fromMap(json.decode(source) as Map<String, dynamic>);

  factory NotificationStorageService.empty() {
    return NotificationStorageService(
      id: 0,
      title: '',
      body: '',
      date: '',
      isRead: 0,
    );
  }
}
