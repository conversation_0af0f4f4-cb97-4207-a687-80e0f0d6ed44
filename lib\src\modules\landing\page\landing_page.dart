import 'package:flutter/material.dart';
import 'package:meta_seo/meta_seo.dart';

import '../../../components/no_mouse_drag_scroll_behavior.dart';
import '../../../components/seo/seo.dart';
import '../../../theme/color_outlet.dart';
import '../controller/landing_page_controller.dart';
import '../widgets/lading-page/affiliate_stores_section/affiliate_stores_section.dart';
import '../widgets/lading-page/footer_widget/footer_widget.dart';
import '../widgets/lading-page/hero_widget/hero_widget.dart';
import '../widgets/lading-page/landing_header/landing_header.dart';

class LandingPage extends StatelessWidget implements Seo {
  final LandingPageController controller;
  const LandingPage({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return SelectionArea(
          child: Scaffold(
            backgroundColor: ColorOutlet.paper,
            body: ScrollConfiguration(
              behavior: NoMouseDragScrollBehavior(),
              child: CustomScrollView(
                slivers: [
                  const LandingHeader(),
                  SliverToBoxAdapter(
                    child: Column(
                      children: [
                        HeroWidget(
                          onAppStorePressed:
                              controller.launchAppStore,
                          onGooglePlayPressed:
                              controller.launchPlayStore,
                        ),
                        const AffiliateStoresSection(),
                        FooterWidget(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  loadSeo() {
    final meta = MetaSEO();
    meta.ogTitle(ogTitle: 'Promobell - O app alerta, você economiza');
    meta.description(
      description:
          'O Promobell é o app ideal para quem quer aproveitar ofertas de produtos, cupons de desconto e frete grátis das melhores lojas do Brasil, como Amazon, Mercado Livre e Magalu. Baixe agora na Google Play ou App Store e comece a economizar!',
    );
    meta.keywords(
      keywords:
          'ofertas, cupons de desconto, frete grátis, alertas de preço, produtos em promoção, marketplaces, Amazon, Magalu, Mercado Livre, app de economia, descontos online, notificações de promoções, rede social de ofertas, produtos favoritos, categorias personalizadas, buscar ofertas, economia inteligente, app de ofertas, alertas inteligentes, cupons exclusivos, o app alerta você economiza, promobell',
    );
  }
}
