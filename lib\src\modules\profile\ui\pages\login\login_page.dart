import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/login_button.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/terms_and_privacy_text.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/text_pattern.dart';
import '../../../controllers/login_controller.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final loginController = Modular.get<LoginController>();

  @override
  void initState() {
    loginController.initAuthListener();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    final screenHeight = MediaQuery.of(context).size.height;

    return AnimatedBuilder(
      animation: loginController,
      builder: (context, _) {
        return Scaffold(
          backgroundColor: ColorOutlet.contentPrimary,
          body: SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(minHeight: screenHeight),
              child: IntrinsicHeight(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(bottom: 100),
                      child: SvgPicture.asset(SvgIcons.login),
                    ),
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.only(
                        top: 32,
                        left: 32,
                        right: 32,
                      ),
                      decoration: const BoxDecoration(
                        color: ColorOutlet.paper,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(24),
                          topRight: Radius.circular(24),
                        ),
                      ),
                      child: Column(
                        spacing: 24,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Column(
                            children: [
                              TextPattern.customText(
                                text:
                                    'Entre, siga e economize de verdade',
                                fontSize: 24,
                                fontWeightOption:
                                    FontWeightOption.bold,
                              ),
                              const SizedBox(height: 8),
                              TextPattern.customText(
                                text:
                                    'Encontre seu produto seguindo perfis que têm tudo a ver com você. O Promobell te alerta sempre que rolar uma oferta ou um cupom.',
                                fontSize: 14,
                                color: ColorOutlet.contentGhost,
                              ),
                            ],
                          ),
                          Visibility(
                            visible: !Platform.isIOS,
                            replacement: LoginButton(
                              icon: SvgIcons.brandsApple,
                              text: 'Continuar com Apple',
                              onPressed:
                                  loginController.isLoadingApple
                                      ? null
                                      : () => loginController
                                          .handleAppleSignIn(context),
                              isLoading:
                                  loginController.isLoadingApple,
                            ),
                            child: LoginButton(
                              icon: SvgIcons.brandsGoogle,
                              text: 'Continuar com Google',
                              onPressed:
                                  loginController.isLoading
                                      ? null
                                      : () => loginController
                                          .handleGoogleSignIn(
                                            context,
                                          ),
                              isLoading: loginController.isLoading,
                            ),
                          ),
                          // LoginButton(
                          //   icon: SvgIcons.brandsApple,
                          //   text: 'Continuar com Apple',
                          //   onPressed: loginController.isLoadingApple ? null : () => loginController.handleAppleSignIn(context),
                          //   isLoading: loginController.isLoadingApple,
                          // ),
                          TermsAndPrivacyText(),
                          SizedBox(height: 14 + bottomPadding),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
