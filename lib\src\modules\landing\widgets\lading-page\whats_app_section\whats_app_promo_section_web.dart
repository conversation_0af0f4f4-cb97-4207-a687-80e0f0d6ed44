import 'package:flutter/material.dart';
import 'package:promobell_landing/src/components/scroll_reveal.dart';

import '../../../../../components/text_pattern.dart';
import '../../../../../theme/color_outlet.dart';
import 'follow_channel_button.dart';

class WhatsAppPromoSection extends StatelessWidget {
  final Function() onWhatsAppButtonPressed;
  const WhatsAppPromoSection({super.key, required this.onWhatsAppButtonPressed});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ScrollReveal(
          offset: Offset(0, -0.7),
          child: TextPattern.customRichText(
            fontSize: 16,
            isSelectable: true,
            children: highlightWords(
              text: 'Alertas também\nno WhatsApp',
              wordsToHighlight: ['Whatsapp'],
              normalStyle: TextStyle(
                color: ColorOutlet.contentSecondary,
                fontSize: 48,
                fontFamily: TextPattern().fontFamily,
                fontWeight: FontWeight.w700,
              ),
              highlightStyle: TextStyle(
                color: ColorOutlet.whatAppColor,
                fontSize: 48,
                fontFamily: TextPattern().fontFamily,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        ScrollReveal(
          offset: Offset(0, -0.5),
          child: TextPattern.customText(
            text: 'Entre no nosso canal oficial e receba as\nmelhores ofertas direto no WhatsApp.',
            fontSize: 20,
            color: ColorOutlet.contentSecondary,
            lineHeight: 1.6,
            overflow: TextOverflow.ellipsis,
            maxLines: 5,
          ),
        ),
        const SizedBox(height: 32),
        ScrollReveal(offset: Offset(0.1, 0), child: FollowChannelButton(onWhatsAppButtonPressed: onWhatsAppButtonPressed)),
      ],
    );
  }
}
