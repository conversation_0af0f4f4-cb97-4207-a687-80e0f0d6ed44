import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../models/categoria_menu.dart';
import '../../db/db.dart';

class GetCategorias {
  final DB _db = DB();
  final SupabaseClient _supabase = Supabase.instance.client;

  Future<CategoriaMenu?> getCategoria(int idCategoria) async {
    try {
      final data =
          await _supabase
              .from(_db.tabelaDeCategorias)
              .select()
              .eq('id', idCategoria)
              .single();

      return CategoriaMenu(
        id: data['id'],
        nome: data['nome'],
        fotoPequena: data['fotoPequena'],
        bio: data['descricao'],
        fotoGrande: data['fotoGrande'],
        video: data['video'],
        cor: data['cor'],
      );
    } catch (e, stackTrace) {
      debugPrint('Erro ao buscar categoria: $e - $stackTrace');
      // Fallback: tentar buscar apenas da lista estática
      return CategoriaMenu.getCategoriaById(idCategoria);
    }
  }

  Future<int> getCategoryFollowersCount(int categoryId) async {
    final response = await _supabase.rpc(
      'count_followers',
      params: {'category_id_param': categoryId},
    );
    return response; // Retorna a contagem de seguidores
  }

  Future<int> getTotalProdutosPorCategoria(
    String categoriaNome,
  ) async {
    try {
      final result = await _supabase.rpc(
        'get_total_produtos_por_categoria',
        params: {'categoria_nome': categoriaNome},
      );
      return result ?? 0;
    } catch (e, stackTrace) {
      debugPrint(
        'Erro ao obter total de produtos da categoria "$categoriaNome" $e, $stackTrace,',
      );
      return 0;
    }
  }
}
