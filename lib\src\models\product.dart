class Product {
  final int id;
  final String plataforma;
  final String urlAfiliado;
  final String urlImagem;
  final String titulo;
  final String categoria;
  final String subcategoria;
  final String descricao;
  final double precoAtual;
  final double precoAntigo;
  final bool ativo;
  final bool menorPreco;
  final bool invalidProduct;

  Product({
    required this.id,
    required this.plataforma,
    required this.urlAfiliado,
    required this.urlImagem,
    required this.titulo,
    required this.categoria,
    required this.subcategoria,
    required this.descricao,
    required this.precoAtual,
    required this.precoAntigo,
    required this.ativo,
    required this.menorPreco,
    required this.invalidProduct,
  });

  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id'] as int,
      plataforma: map['plataforma']?.toString() ?? '',
      urlAfiliado: map['url_afiliado']?.toString() ?? '',
      urlImagem: map['url_imagem']?.toString() ?? '',
      titulo: map['titulo']?.toString() ?? '',
      categoria: map['categoria']?.toString() ?? '',
      subcategoria: map['subcategoria']?.toString() ?? '',
      descricao: map['descricao']?.toString() ?? '',
      precoAtual: _parseDouble(map['preco_atual']),
      precoAntigo: _parseDouble(map['preco_antigo']),
      ativo: map['ativo'] == true,
      menorPreco: map['menor_preco'] == true,
      invalidProduct: map['invalidProduct'] == true,
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  factory Product.mock() {
    return Product(
      id: 1,
      plataforma: 'Mercado Livre',
      urlAfiliado:
          'https://www.mercadolivre.com.br/social/akipromocoes?matt_tool=86110016&forceInApp=true&ref=BI9Jo%2FtK%2FNsNKKr5HkRiMy1ZPSrBHF%2BvmMP1dnpXsDU%2F1%2FtQFaDk%2FuLQlRYpjuche9Sl1kZQTTvNbbff193U20PeIFTCwGZNm8juI3OilVq1TW8kHb03tForBJlvAlEYXFHnOK8fUZ28QvDLuBpFwnFaKrne5NRccUFtXTl%2BxprlGWG9yaGpGhOE6hmk9e3g5LJLCg%3D%3D',
      urlImagem:
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTk99N0oTnyLXsM_oQXN62lT_5vjfbFRDl05Q&s',
      titulo: 'Produto não encontrado!',
      categoria: 'Produto não encontrado!',
      subcategoria: 'Produto não encontrado!',
      descricao: 'Produto não encontrado!',
      precoAtual: 0,
      precoAntigo: 0,
      ativo: true,
      menorPreco: true,
      invalidProduct: false,
    );
  }
}
