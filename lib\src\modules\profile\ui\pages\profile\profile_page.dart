import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:promobell/src/modules/profile/ui/widgets/profile/whats_app_promo_card.dart';
import 'package:promobell/src/services/navigation/scroll_services.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/custom_snack_bar.dart';
import '../../../controllers/login_controller.dart';
import '../../../controllers/profile_controller.dart';
import '../../widgets/profile/blur_effect_image.dart';
import '../../widgets/profile/card_container.dart';
import '../../widgets/profile/expandable_profile_card.dart';
import '../../widgets/profile/icon_text_action_button.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final controller = Modular.get<ProfileController>();
  final loginController = Modular.get<LoginController>();
  final scrollService = Modular.get<ScrollService>();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    scrollService.registerScrollController(
      'profilePage',
      _scrollController,
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      controller.loadUserData();
      controller.cancelEditing();
    });

    _scrollController.addListener(() {
      if (!mounted) return;
      if (_scrollController.offset < 0) {
        _scrollController.jumpTo(0);
      }
    });
  }

  @override
  void dispose() {
    if (_scrollController.hasClients) {
      scrollService.saveScrollPosition('profilePage');
    }
    scrollService.unregisterScrollController('profilePage');
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final paddingBottom = MediaQuery.of(context).padding.bottom;

    return AnimatedBuilder(
      animation: Listenable.merge([controller, loginController]),
      builder:
          (context, child) => CustomScrollView(
            controller: _scrollController,
            slivers: [
              SliverToBoxAdapter(
                child: Stack(
                  alignment: Alignment.topCenter,
                  clipBehavior: Clip.none,
                  children: [
                    ColoredBox(color: ColorOutlet.surface),
                    BlurEffectImage(
                      image: controller.userImage ?? '',
                      tempImage: controller.tempImageWeb,
                    ),
                    ExpandableProfileCard(controller: controller),
                  ],
                ),
              ),
              SliverToBoxAdapter(
                child: AnimationLimiter(
                  child: AbsorbPointer(
                    absorbing: controller.isEditing,
                    child: ImageFiltered(
                      imageFilter: ImageFilter.blur(
                        sigmaX: controller.isEditing ? 2 : 0,
                        sigmaY: controller.isEditing ? 2 : 0,
                      ),
                      child: Container(
                        color: ColorOutlet.surface,
                        child: Column(
                          spacing: 16,
                          children: AnimationConfiguration.toStaggeredList(
                            duration: const Duration(
                              milliseconds: 500,
                            ),
                            childAnimationBuilder:
                                (widget) => SlideAnimation(
                                  verticalOffset: 50.0,
                                  child: FadeInAnimation(
                                    child: widget,
                                  ),
                                ),
                            children: [
                              SizedBox(height: 0),
                              CardContainer(
                                child: Column(
                                  spacing: 8,
                                  children: [
                                    IconTextActionButton(
                                      onPressed: () {
                                        if (!mounted) return;
                                        Modular.to.pushNamed(
                                          '/profile/save_products',
                                        ); // Use push ao invés de pushReplacement
                                      },
                                      icon: SvgIcons.markerBookmark,
                                      label: 'Ofertas salvas',
                                    ),
                                    // IconTextActionButton(
                                    //   onPressed: () {},
                                    //   icon: SvgIcons.actionNotification,
                                    //   label: 'Notificações',
                                    // ),
                                    // IconTextActionButton(
                                    //   onPressed: () {},
                                    //   icon: SvgIcons.actionTheme,
                                    //   label: 'Aparência',
                                    // ),
                                  ],
                                ),
                              ),
                              CardContainer(
                                child: Column(
                                  spacing: 8,
                                  children: [
                                    IconTextActionButton(
                                      onPressed: () {
                                        if (!mounted) return;
                                        Modular.to.pushNamed(
                                          '/profile/about_the_promobell',
                                        ); // Use push ao invés de pushReplacement
                                      },
                                      icon: SvgIcons.feedbackHelp,
                                      label: 'Sobre o Promobell',
                                    ),
                                    IconTextActionButton(
                                      onPressed:
                                          () => Modular.to.pushNamed(
                                            '/profile/privacy_use',
                                          ),
                                      icon: SvgIcons.securityTerms,
                                      label: 'Termos de uso',
                                    ),
                                    IconTextActionButton(
                                      onPressed:
                                          () => Modular.to.pushNamed(
                                            '/profile/privacy_politics',
                                          ),
                                      icon: SvgIcons.securityLock,
                                      label:
                                          'Política de Privacidade',
                                    ),
                                  ],
                                ),
                              ),
                              WhatsAppPromoCard(),
                              CardContainer(
                                child: IconTextActionButton(
                                  onPressed: () {
                                    CustomSnackBar.show(
                                      context: context,
                                      message: 'Saindo da conta',
                                      noIcon: false,
                                    );
                                    Future.delayed(
                                      Duration(milliseconds: 6),
                                      () {
                                        loginController.logOut();
                                      },
                                    );
                                  },
                                  icon: SvgIcons.arrowTipBoxRight,
                                  label: 'Sair da conta',
                                  isLoading:
                                      loginController
                                          .isLoadingSignOut,
                                ),
                              ),
                              SizedBox(height: paddingBottom),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
    );
  }
}
