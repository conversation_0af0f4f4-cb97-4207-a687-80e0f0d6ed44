import 'package:flutter/material.dart';
import 'package:promobell_landing/src/modules/landing/widgets/lading-page/whats_app_section/channel_header_mockup.dart';

import '../../../../../components/responsive/responsive_padding.dart';
import '../../../../../theme/color_outlet.dart';
import 'whats_app_promo_section_web.dart';

class WhatsAppSectionWeb extends StatelessWidget {
  const WhatsAppSectionWeb({super.key, required this.onWhatsAppButtonPressed});

  final Function() onWhatsAppButtonPressed;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 86),
          child: Container(
            height: 400,
            padding: EdgeInsets.only(top: 56, left: 56, right: 44),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(24)),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [ColorOutlet.paper, Color(0xFFF2EADF)],
              ),
            ),
            child: Row(children: [WhatsAppPromoSection(onWhatsAppButtonPressed: onWhatsAppButtonPressed)]),
          ),
        ),
        ResponsivePadding(
          minPadding: 10,
          maxPadding: 56,
          paddingBuilder: (padding) => EdgeInsets.only(right: padding),
          breakpoints: {600: 0.04, 900: 0.06, 1200: 0.08, double.infinity: 0.08},
          child: Align(
            alignment: Alignment.topRight,
            child: Stack(clipBehavior: Clip.none, children: [ChannelHeaderMockup()]),
          ),
        ),
      ],
    );
  }
}
