import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/custom_snack_bar.dart';
import '../../../../../components/input_field_pattern.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../categories/ui/widgets/detail/custom_dialog.dart';
import '../../../controllers/profile_controller.dart';

class UserProfileCardEditing extends StatefulWidget {
  final String? name;
  final String? surname;
  final String? birthDate;
  final String email;
  final String? joinedDate;
  final VoidCallback onPressed;
  final VoidCallback onTapImage;

  const UserProfileCardEditing({
    required this.onPressed,
    required this.name,
    required this.email,
    required this.joinedDate,
    required this.onTapImage,
    required this.surname,
    required this.birthDate,
    super.key,
  });

  @override
  State<UserProfileCardEditing> createState() =>
      _UserProfileCardEditingState();
}

class _UserProfileCardEditingState
    extends State<UserProfileCardEditing> {
  final controller = Modular.get<ProfileController>();
  @override
  void initState() {
    controller.nameController?.text = widget.name ?? '';
    controller.surnameController?.text = widget.surname ?? '';
    controller.birthDateController?.text = widget.birthDate ?? '';
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return Form(
          key: controller.formKeyProfile,
          child: SingleChildScrollView(
            physics: NeverScrollableScrollPhysics(),
            child: Column(
              spacing: 16,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 16),
                  child: TextPattern.customText(
                    text: 'Sobre você',
                    fontWeightOption: FontWeightOption.bold,
                  ),
                ),
                InputFieldPattern(
                  controller: controller.nameController,
                  hintText: 'Nome',
                  validator:
                      (value) => controller.validator(
                        value: value,
                        name: controller.nameController,
                      ),
                ),
                InputFieldPattern(
                  controller: controller.surnameController,
                  hintText: 'Sobrenome (opcional)',
                  validator:
                      (value) =>
                          controller.validarCampoSobrenome(value),
                ),
                InputFieldPattern(
                  controller: controller.birthDateController,
                  enabled: false,
                  textColor: ColorOutlet.contentGhost,
                  initialValue:
                      widget.birthDate?.isNotEmpty == true
                          ? DateFormat('dd/MM/yyyy').format(
                            DateTime.tryParse(widget.birthDate!) ??
                                DateTime.now(),
                          )
                          : '',
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 16),
                  child: TextPattern.customText(
                    text: 'Seu email',
                    fontWeightOption: FontWeightOption.bold,
                  ),
                ),
                Stack(
                  children: [
                    InputFieldPattern(
                      initialValue: widget.email,
                      textColor: ColorOutlet.contentGhost,
                      enabled: false,
                      suffixIcon: SvgPicture.asset(
                        SvgIcons.feedbackInfo,
                      ),
                      suffixIconTap: () async {
                        String plataforma = await controller
                            .getPlataformaAutenticacao(widget.email);
                        if (context.mounted) {
                          CustomDialog.show(
                            context,
                            onConfirm: () {},
                            onCancel: () {},
                            buttonOnly: true,
                            title: 'Email cadastrado',
                            message:
                                'Seu cadastro foi feito utilizando sua conta $plataforma. Por isso, o email associado não pode ser alterado diretamente no nosso app.\n\nSe precisar usar outro email, será necessário criar uma nova conta com a credencial desejada.',
                          );
                        }
                      },
                    ),
                  ],
                ),
                Align(
                  alignment: Alignment.center,
                  child: TextButton(
                    onPressed: () {
                      CustomDialog.show(
                        context,
                        textOnConfirm: 'Excluir conta',
                        onConfirm:
                            () => Modular.to.pushReplacementNamed(
                              '/profile/delete_account',
                            ),
                        onCancel: () {},
                        buttonOnly: false,
                        title: 'Excluir conta',
                        message:
                            'Ao excluir sua conta, todos os seus dados serão removidos permanentemente, e não será possível recuperar o acesso.\n\nCaso queira utilizar o Promobell novamente no futuro, será necessário criar uma nova conta do zero.\n\nTem certeza de que deseja continuar?',
                      );
                    },
                    child: TextPattern.customText(
                      text: 'Excluir conta',
                      fontWeightOption: FontWeightOption.regular,
                      color: ColorOutlet.feedbackError,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 24),
                  child: buildActionButtons(context),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget buildActionButtons(BuildContext context) {
    return Row(
      spacing: 16,
      children: [
        SizedBox(
          height: 44,
          child: TextButton(
            onPressed: widget.onPressed,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 6),
              child: TextPattern.customText(text: 'Cancelar'),
            ),
          ),
        ),
        Expanded(
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              minimumSize: Size(
                MediaQuery.of(context).size.width * .48,
                44,
              ),
              backgroundColor: ColorOutlet.contentPrimary,
              overlayColor: ColorOutlet.systemBorderDisabled
                  .withValues(alpha: 0.3),
            ),
            onPressed:
                controller.loadSaving
                    ? null
                    : () async {
                      if (controller.formKeyProfile.currentState
                              ?.validate() ??
                          false) {
                        controller.loadSaving = true;
                        try {
                          await controller.updateProfile();

                          controller.loadSaving = false;

                          // Exibir SnackBar de sucesso
                          if (context.mounted) {
                            CustomSnackBar.show(
                              context: context,
                              message: "Seus ajustes foram salvos.",
                              icon: SvgIcons.feedbackCheck,
                            );
                          }
                        } catch (e) {
                          controller.loadSaving = false;

                          // Exibir SnackBar de erro
                          if (context.mounted) {
                            CustomSnackBar.show(
                              context: context,
                              message:
                                  "Ocorreu um erro ao salvar seus ajustes. Tente novamente mais tarde.",
                              icon:
                                  SvgIcons
                                      .feedbackCaution, // Supondo que você tenha um ícone de erro
                            );
                          }
                        }
                      }

                      // Resetar o formulário após 2 segundos
                      await Future.delayed(
                        const Duration(seconds: 2),
                        () {
                          controller.formKeyProfile.currentState
                              ?.reset();
                        },
                      );
                    },
            child:
                controller.loadSaving
                    ? SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 1.5,
                      ),
                    )
                    : TextPattern.customText(
                      text: 'Salvar',
                      color: ColorOutlet.contentTertiary,
                    ),
          ),
        ),
      ],
    );
  }
}
