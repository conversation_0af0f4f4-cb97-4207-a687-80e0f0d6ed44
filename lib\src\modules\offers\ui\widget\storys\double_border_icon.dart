import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import '../../../../../../theme/color_outlet.dart';

class DoubleBorderIcon extends StatefulWidget {
  final Widget child;
  final Color borderColor;
  final Color viewingBorder;
  final Color backgroundColor;
  final double padding;
  final bool loading;
  final bool isVisualized;
  final bool empty;

  const DoubleBorderIcon({
    required this.borderColor,
    this.viewingBorder = ColorOutlet.contentPrimary,
    this.backgroundColor = ColorOutlet.paper,
    this.isVisualized = false,
    required this.child,
    this.padding = 14,
    this.loading = false,
    this.empty = false,
    super.key,
  });

  @override
  State<DoubleBorderIcon> createState() => _DoubleBorderIconState();
}

class _DoubleBorderIconState extends State<DoubleBorderIcon> {
  bool loadingLottie = true;

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration(seconds: 3), () {
      if (!mounted) return;
      setState(() {
        loadingLottie = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: widget.backgroundColor,
            borderRadius: BorderRadius.all(Radius.circular(96)),
            border:
                !loadingLottie && !widget.empty
                    ? Border.all(
                      color: widget.viewingBorder,
                      width: 3,
                    )
                    : Border.all(
                      color: ColorOutlet.systemBorderDisabled,
                      width: 3,
                    ),
          ),
          child: Container(
            width: 70,
            height: 70,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(96)),
              border: Border.all(color: widget.borderColor, width: 2),
            ),
            padding: EdgeInsets.all(widget.padding),
            child: widget.child,
          ),
        ),
        Visibility(
          visible:
              loadingLottie && !widget.empty && !widget.isVisualized,
          child: Lottie.asset(
            'assets/lottie/lottie-story.json',
            width: 80,
            height: 80,
            fit: BoxFit.cover,
          ),
        ),
        Visibility(
          visible: widget.loading,
          child: Lottie.asset(
            'assets/lottie/lottie-story.json',
            width: 80,
            height: 80,
            fit: BoxFit.cover,
          ),
        ),
        Visibility(
          visible: widget.empty,
          child: Lottie.asset(
            'assets/lottie/lottie-story.json',
            width: 80,
            height: 80,
            fit: BoxFit.cover,
          ),
        ),
      ],
    );
  }
}
