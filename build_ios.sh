#!/bin/bash

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${YELLOW}Iniciando processo de build para iOS com flavors${NC}"

if [ "$1" == "" ]; then
  echo -e "${YELLOW}Por favor, especifique o flavor: dev ou prod${NC}"
  echo -e "Exemplo: ./build_ios.sh dev"
  exit 1
fi

FLAVOR=$1
ENV_FILE=""
MAIN_DART_FILE=""
FIREBASE_PLIST=""

if [ "$FLAVOR" == "dev" ]; then
  ENV_FILE=".envDesenvolvimento"
  SCHEME="dev"
  MAIN_DART_FILE="lib/flavors/main_dev.dart"
  FIREBASE_PLIST="ios/Firebase/GoogleService-Info-dev.plist"
elif [ "$FLAVOR" == "prod" ]; then
  ENV_FILE=".env"
  SCHEME="Runner"
  MAIN_DART_FILE="lib/flavors/main_prod.dart"
  FIREBASE_PLIST="ios/Firebase/GoogleService-Info-prod.plist"
else
  echo -e "${YELLOW}Flavor inválido. Use 'dev' ou 'prod'.${NC}"
  exit 1
fi

echo -e "${GREEN}Flavor selecionado: $FLAVOR${NC}"
echo -e "${GREEN}Arquivo de ambiente: $ENV_FILE${NC}"
echo -e "${GREEN}Scheme: $SCHEME${NC}"
echo -e "${GREEN}Arquivo main: $MAIN_DART_FILE${NC}"
echo -e "${GREEN}Firebase plist: $FIREBASE_PLIST${NC}"

# Verificar se o arquivo .env existe
if [ ! -f "$ENV_FILE" ]; then
  echo -e "${YELLOW}Arquivo $ENV_FILE não encontrado no diretório atual: $(pwd)${NC}"
  echo -e "${YELLOW}Arquivos no diretório atual:${NC}"
  ls -la | grep -E "\.env|\.envDesenvolvimento"
  echo -e "${YELLOW}O arquivo $ENV_FILE é necessário para o build. Por favor, crie-o.${NC}"
  exit 1
else
  echo -e "${GREEN}Arquivo $ENV_FILE encontrado.${NC}"
  # Verificar formato do arquivo
  if ! grep -q "^API_URL=" "$ENV_FILE"; then
    echo -e "${YELLOW}Aviso: API_URL não encontrado no arquivo $ENV_FILE${NC}"
  fi
fi

# Copiar o arquivo GoogleService-Info.plist correto
if [ -f "$FIREBASE_PLIST" ]; then
  echo -e "${YELLOW}Copiando arquivo Firebase para o flavor $FLAVOR...${NC}"
  cp "$FIREBASE_PLIST" "ios/Runner/GoogleService-Info.plist"
  echo -e "${GREEN}Arquivo Firebase copiado com sucesso!${NC}"
else
  echo -e "${YELLOW}Arquivo Firebase não encontrado: $FIREBASE_PLIST${NC}"
  echo -e "${YELLOW}Verifique se o arquivo existe no caminho especificado.${NC}"
  echo -e "${YELLOW}Continuando sem copiar o arquivo Firebase...${NC}"
fi

echo -e "${YELLOW}Limpando o projeto...${NC}"
flutter clean

echo -e "${YELLOW}Obtendo dependências...${NC}"
flutter pub get

echo -e "${YELLOW}Instalando pods...${NC}"
cd ios && pod install && cd ..

echo -e "${YELLOW}Preparando build iOS com dart defines...${NC}"
flutter build ios --flavor $FLAVOR --dart-define-from-file=$ENV_FILE -t $MAIN_DART_FILE --no-codesign

echo -e "${YELLOW}Abrindo o Xcode para finalizar o build...${NC}"
cd ios && open Runner.xcworkspace

echo -e "${GREEN}Preparação concluída!${NC}"
echo -e "${YELLOW}Agora no Xcode:${NC}"
echo -e "1. Selecione o scheme '$SCHEME'"
echo -e "2. Selecione 'Any iOS Device (arm64)' como dispositivo de destino"
echo -e "3. Vá para Product > Archive"
echo -e "4. Após a conclusão, distribua o aplicativo conforme necessário"

exit 0
