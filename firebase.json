{"hosting": {"public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "redirects": [{"source": "/home", "destination": "/", "type": 301}, {"source": "/index.html", "destination": "/", "type": 301}, {"source": "/sobre.html", "destination": "/sobre", "type": 301}, {"source": "/termos.html", "destination": "/termos", "type": 301}, {"source": "/privacidade.html", "destination": "/privacidade", "type": 301}], "rewrites": [{"source": "/.well-known/assetlinks.json", "destination": "/.well-known/assetlinks.json"}, {"source": "/.well-known/apple-app-site-association", "destination": "/.well-known/apple-app-site-association"}, {"source": "/sitemap.xml", "destination": "/sitemap.xml"}, {"source": "/robots.txt", "destination": "/robots.txt"}, {"source": "/product", "destination": "/index.html"}, {"source": "/category", "destination": "/index.html"}, {"source": "**", "destination": "/index.html"}], "headers": [{"source": "/.well-known/assetlinks.json", "headers": [{"key": "Content-Type", "value": "application/json"}, {"key": "Access-Control-Allow-Origin", "value": "*"}]}, {"source": "/.well-known/apple-app-site-association", "headers": [{"key": "Content-Type", "value": "application/json"}]}, {"source": "/sitemap.xml", "headers": [{"key": "Content-Type", "value": "application/xml"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Cache-Control", "value": "public, max-age=86400"}]}, {"source": "/robots.txt", "headers": [{"key": "Content-Type", "value": "text/plain"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Cache-Control", "value": "public, max-age=86400"}]}, {"source": "**/*.html", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "**/*.@(jpg|jpeg|gif|png|svg|webp|ico|woff|woff2)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}}