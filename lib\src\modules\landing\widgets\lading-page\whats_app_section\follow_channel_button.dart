import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../components/text_pattern.dart';
import '../../../../../theme/color_outlet.dart';
import '../../../../../theme/svg_icons.dart';

class FollowChannelButton extends StatelessWidget {
  const FollowChannelButton({super.key, required this.onWhatsAppButtonPressed});

  final Function() onWhatsAppButtonPressed;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 48,
      child: ElevatedButton(
        onPressed: onWhatsAppButtonPressed,
        style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor: const Color(0xFF25D366),
          foregroundColor: ColorOutlet.paper,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 4.5),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        ),
        child: Row(
          spacing: 4,
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(SvgIcons.whats, colorFilter: ColorFilter.mode(ColorOutlet.paper, BlendMode.srcIn)),
            TextPattern.customText(
              text: 'Seguir canal',
              fontSize: 16,
              fontWeightOption: FontWeightOption.regular,
              color: ColorOutlet.paper,
            ),
          ],
        ),
      ),
    );
  }
}
