import '../../../../../../theme/color_outlet.dart';
import 'package:flutter/material.dart';

class DashedSeparatorRow extends StatelessWidget {
  const DashedSeparatorRow({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: List.generate(
        19,
        (index) => Container(
          width: 6,
          height: 6,
          decoration: BoxDecoration(
            color: ColorOutlet.surface,
            shape: BoxShape.circle,
          ),
        ),
      ),
    );
  }
}
