import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../components/text_pattern.dart';
import '../../../../../theme/color_outlet.dart';

class TextLadingPage extends StatelessWidget {
  final String text;
  final String? route;
  final void Function()? onPressed;
  final bool icon;
  final String? svg;

  const TextLadingPage({required this.text, this.route, this.onPressed, this.icon = false, this.svg, super.key});

  @override
  Widget build(BuildContext context) {
    final isActive = Modular.to.path == route;

    return SizedBox(
      height: 40,
      child: TextButton(
        onPressed: onPressed,
        style: TextButton.styleFrom(
          foregroundColor: ColorOutlet.contentDisabled,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          padding: const EdgeInsets.symmetric(horizontal: 16),
        ),
        child:
            icon
                ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  spacing: 8,
                  children: [
                    SvgPicture.asset(
                      svg ?? '',
                      colorFilter: ColorFilter.mode(ColorOutlet.contentSecondary, BlendMode.srcIn),
                    ),
                    TextPattern.customText(
                      text: text,
                      color: ColorOutlet.contentSecondary,
                      fontSize: 16,
                      fontWeightOption: FontWeightOption.regular,
                    ),
                  ],
                )
                : TextPattern.customText(
                  text: text,
                  color: isActive ? ColorOutlet.contentPrimary : ColorOutlet.contentSecondary,
                  fontSize: 16,
                  fontWeightOption: FontWeightOption.regular,
                ),
      ),
    );
  }
}
