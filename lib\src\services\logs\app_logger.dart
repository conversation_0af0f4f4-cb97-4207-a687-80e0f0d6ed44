import 'package:flutter/material.dart';

class AppLogger {
  static void logError(String message, dynamic error, StackTrace? stackTrace) {
    debugPrint('ERROR: $message');
    debugPrint('Error details: $error');
    if (stackTrace != null) debugPrint('StackTrace: $stackTrace');
  }

  static void logInfo(String message) {
    debugPrint('INFO: $message');
  }

  static void logError2(String message, dynamic error) {
    debugPrint('ERROR: $message');
    debugPrint('Error details: $error');
  }
}
