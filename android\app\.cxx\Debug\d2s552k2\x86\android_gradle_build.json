{"buildFiles": ["H:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["H:\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "H:\\projetos\\chegou mercado\\promobell\\android\\app\\.cxx\\Debug\\d2s552k2\\x86", "clean"]], "buildTargetsCommandComponents": ["H:\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "H:\\projetos\\chegou mercado\\promobell\\android\\app\\.cxx\\Debug\\d2s552k2\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "H:\\Android\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "H:\\Android\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}