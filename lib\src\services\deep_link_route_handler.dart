import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:lottie/lottie.dart';
import 'package:promobell/theme/color_outlet.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../core/base/controllers/base_controller.dart/base_controller.dart';
import '../models/categorias_menu.dart';
import '../models/product.dart';
import '../modules/categories/categories_module.dart';
import '../modules/offers/offers_module.dart';
import 'logs/app_logger.dart';

class DeepLinkRouteHandler extends StatefulWidget {
  final Map<String, dynamic>? params;
  final String routeType;

  const DeepLinkRouteHandler({
    super.key,
    required this.routeType,
    this.params,
  });

  @override
  State<DeepLinkRouteHandler> createState() =>
      _DeepLinkRouteHandlerState();
}

class _DeepLinkRouteHandlerState extends State<DeepLinkRouteHandler> {
  final baseController = Modular.get<BaseController>();

  @override
  void initState() {
    super.initState();
    _processDeepLink();
  }

  Future<void> _processDeepLink() async {
    if (widget.routeType == 'product') {
      await _handleProductRoute();
    } else if (widget.routeType == 'category') {
      await _handleCategoryRoute();
    } else {
      _navigateToHome();
    }
  }

  Future<void> _handleProductRoute() async {
    try {
      final String? id = widget.params?['id'];

      if (id == null || id.isEmpty || id == "0") {
        _navigateToHome();
        return;
      }

      final product = await _getProduct(id);

      Modular.to.pushNamed(
        '/offers${OffersModule.productDetails}',
        arguments: product,
      );
    } catch (e) {
      _navigateToHome();
    }
  }

  Future<void> _handleCategoryRoute() async {
    try {
      // Definir o índice para a página de categorias (1)
      final baseController = Modular.get<BaseController>();
      baseController.navPage(1);

      final categoria = widget.params?['categoria'];
      if (categoria != null && categoria is CategoriaMenu) {
        // Navegar para a categoria específica dentro do PageView
        Modular.to.navigate(
          '/categories${CategoriesModule.detailsCategoryPage}',
          arguments: categoria,
        );
        return;
      }

      final idParam = widget.params?['id'];
      if (idParam != null) {
        final int id = int.tryParse(idParam.toString()) ?? -1;
        final categoriaById = CategoriaMenu.getCategoriaById(id);

        if (categoriaById != null) {
          // Navegar para a categoria específica dentro do PageView
          Modular.to.navigate(
            '/categories${CategoriesModule.detailsCategoryPage}',
            arguments: categoriaById,
          );
          return;
        } else {
          AppLogger.logInfo(
            '⚠️ DeepLinkRouteHandler: Categoria não encontrada para o ID: $id',
          );
        }
      }

      final String? categoryName = widget.params?['name'];
      if (categoryName != null && categoryName.isNotEmpty) {
        final CategoriaMenu categoriaByName =
            CategoriaMenu.getCategoriaByNome(categoryName);
        // Navegar para a categoria específica dentro do PageView
        Modular.to.navigate(
          '/categories${CategoriesModule.detailsCategoryPage}',
          arguments: categoriaByName,
        );
        return;
      }

      baseController.navPage(1);
      Modular.to.navigate('/home');
    } catch (e, stackTrace) {
      AppLogger.logError(
        'DeepLinkRouteHandler: Erro ao processar rota de categoria',
        e,
        stackTrace,
      );
      final baseController = Modular.get<BaseController>();
      baseController.navPage(1);
      Modular.to.navigate('/home');
    }
  }

  void _navigateToHome() {
    if (mounted) {
      final baseController = Modular.get<BaseController>();

      if (widget.routeType == 'category') {
        baseController.navPage(1);
        Modular.to.navigate('/home');
      } else {
        baseController.navPage(0);
        Modular.to.navigate('/home');
      }
    }
  }

  Future<Product> _getProduct(String id) async {
    final int idProduto = int.parse(id);
    final supabase = Supabase.instance.client;

    try {
      final data =
          await supabase
              .from('produtos_cadastro')
              .select()
              .eq('id', idProduto)
              .select();

      if (data.isEmpty) {
        throw Exception(
          'Produto não encontrado para o ID: $idProduto',
        );
      }

      final Product produto = Product.fromMap(data.first);
      AppLogger.logInfo(
        'DeepLinkRouteHandler: Produto recuperado com sucesso: ${produto.titulo}',
      );
      return produto;
    } catch (e, stackTrace) {
      AppLogger.logError(
        'DeepLinkRouteHandler: Erro ao buscar produto',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorOutlet.contentPrimary,
      body: Center(
        child: Lottie.asset(
          'assets/lottie/lottie-logo.json',
          width: 120,
          height: 120,
          fit: BoxFit.cover,
          repeat: true,
          animate: true,
        ),
      ),
    );
  }
}
