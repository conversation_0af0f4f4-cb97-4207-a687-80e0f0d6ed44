import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class ButtonIconWithBackground extends StatelessWidget {
  final VoidCallback? onPressed;
  final String iconPath;
  final bool isSared;

  const ButtonIconWithBackground({
    required this.iconPath,
    this.isSared = false,
    required this.onPressed,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 40,
      height: 40,
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(16),
          child: Center(
            child: isSared
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 1.5),
                  )
                : SvgPicture.asset(iconPath),
          ),
        ),
      ),
    );
  }
}
