import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../components/responsive/responsive_padding_wrappe.dart';
import '../../../../../components/scroll_reveal.dart';
import '../../../../../theme/color_outlet.dart';
import 'find_and_enjoy_section.dart';

class AffiliateStoresSection extends StatelessWidget {
  const AffiliateStoresSection({super.key});

  @override
  Widget build(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width > 900;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: [0.0, 0.7],
          colors: [ColorOutlet.surface, ColorOutlet.paper],
        ),
      ),
      padding: EdgeInsets.only(bottom: isDesktop ? 106 : 32, top: isDesktop ? 0 : 112),
      child:
          isDesktop
              ? ResponsivePaddingWrapper(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Flexible(child: FindAndEnjoySection()),
                    SizedBox(width: 16),
                    BrandShowcaseRow(),
                    SizedBox(width: 90),
                  ],
                ),
              )
              : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ResponsivePaddingWrapper.mobile(minPadding: 16, child: BrandShowcaseRow(isMobile: true)),
                  SizedBox(height: 44.5),
                  ResponsivePaddingWrapper.mobile(child: FindAndEnjoySection(isMobile: true)),
                ],
              ),
    );
  }
}

class BrandShowcaseRow extends StatelessWidget {
  final bool isMobile;

  const BrandShowcaseRow({this.isMobile = false, super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final totalPadding = isMobile ? 76.0 : 96.0;
        final totalWidth = constraints.maxWidth - totalPadding;

        const amazonWeight = 1.0;
        const mercadoLivreWeight = 0.6;
        const magaluWeight = 1.0;
        const totalWeight = amazonWeight + mercadoLivreWeight + magaluWeight;

        final unit = totalWidth / totalWeight;

        final amazonWidth = unit * amazonWeight;
        final mercadoLivreWidth = unit * mercadoLivreWeight;
        final magaluWidth = unit * magaluWeight;

        return Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            ScrollReveal(
              offset: Offset(0, 3),
              child: SvgPicture.asset('assets/logos/amazon-logo-grande.svg', width: isMobile ? amazonWidth : null),
            ),
            SizedBox(width: isMobile ? 38 : 48),
            ScrollReveal(
              offset: Offset(0, 2),
              child: SvgPicture.asset(
                'assets/logos/mercado-livre-logo-grande.svg',
                width: isMobile ? mercadoLivreWidth : null,
              ),
            ),
            SizedBox(width: isMobile ? 38 : 48),
            ScrollReveal(
              offset: Offset(0, 1),
              child: SvgPicture.asset('assets/logos/magalu-logo-grande.svg', width: isMobile ? magaluWidth : null),
            ),
          ],
        );
      },
    );
  }
}
