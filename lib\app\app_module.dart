import 'package:flutter_modular/flutter_modular.dart';

import '../src/modules/landing/controller/landing_page_controller.dart';
import '../src/modules/landing/controller/privacy_page_controller.dart';
import '../src/modules/landing/modules/landing_page_module.dart';
import '../src/services/supabase/categorias/get/get_categorias.dart';
import '../src/services/supabase/db/db.dart';
import '../src/services/supabase/produtos/get/get_produtos.dart';

class AppModule extends Module {
  @override
  final List<Bind> binds = [
    // Serviços do Supabase
    Bind.singleton((i) => DB()),
    Bind.singleton((i) => GetProdutos()),
    Bind.singleton((i) => GetCategorias()),

    // Controllers
    Bind((i) => LandingPageController()),
    Bind((i) => PrivacyPageController()),
  ];

  @override
  final List<ModularRoute> routes = [
    ModuleRoute(Modular.initialRoute, module: LandingPageModule()),
    // ModuleRoute('', module: PolicyModule()),
  ];
}
