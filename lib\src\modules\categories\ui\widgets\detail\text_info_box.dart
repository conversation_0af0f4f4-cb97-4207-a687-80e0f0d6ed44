import '../../../../../components/text_pattern.dart';
import '../../../../../../theme/color_outlet.dart';
import 'package:flutter/material.dart';

class TextInfoBox extends StatelessWidget {
  final String text;
  final String label;

  const TextInfoBox({
    required this.text,
    required this.label,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TextPattern.customText(
          text: text,
          fontSize: 16,
          color: ColorOutlet.contentPrimary,
          fontWeightOption: FontWeightOption.semiBold,
        ),
        TextPattern.customText(
          text: label,
          fontSize: 12,
          color: ColorOutlet.contentSecondary,
        ),
      ],
    );
  }
}
