import 'package:flutter/material.dart';

import '../../../../components/text_pattern.dart';
import '../../../../theme/color_outlet.dart';

class CustomRichText extends StatelessWidget {
  final String text;
  final bool isPrimaryColor;

  const CustomRichText({
    this.isPrimaryColor = false,
    required this.text,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return isPrimaryColor
        ? TextPattern.customRichText(
            fontSize: 16,
            isSelectable: true,
            children: highlightWords(
              text: text,
              wordsToHighlight: [
                'Whatsapp',
                'instagram.Promobell',
                '<EMAIL>'
              ],
              normalStyle: TextStyle(
                color: ColorOutlet.contentSecondary,
                fontSize: 16,
                fontFamily: TextPattern().fontFamily,
                fontWeight: FontWeight.w400,
              ),
              highlightStyle: TextStyle(
                color: ColorOutlet.contentPrimary,
                fontSize: 16,
                fontFamily: TextPattern().fontFamily,
                fontWeight: FontWeight.w400,
              ),
            ),
          )
        : TextPattern.customRichText(
            fontSize: 16,
            isSelectable: true,
            children: highlightWords(
              text: text,
              wordsToHighlight: [
                'Promobell',
                'Amazon',
                'Mercado Livre',
                'Magazine Luiza'
              ],
              normalStyle: TextStyle(
                color: ColorOutlet.contentSecondary,
                fontSize: 16,
                fontFamily: TextPattern().fontFamily,
                fontWeight: FontWeight.w400,
              ),
              highlightStyle: TextStyle(
                color: ColorOutlet.contentSecondary,
                fontSize: 16,
                fontFamily: TextPattern().fontFamily,
                fontWeight: FontWeight.bold,
              ),
            ),
          );
  }
}
