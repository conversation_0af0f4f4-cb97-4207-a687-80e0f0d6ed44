import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

import '../../../../../theme/color_outlet.dart';

class LoopingVideoBackground extends StatefulWidget {
  final String videoAssets; // pode ser um URL ou um asset
  const LoopingVideoBackground({super.key, required this.videoAssets});

  @override
  State<LoopingVideoBackground> createState() => _LoopingVideoBackgroundState();
}

class _LoopingVideoBackgroundState extends State<LoopingVideoBackground> {
  late VideoPlayerController _controller;

  @override
  void initState() {
    super.initState();
    _controller =
        VideoPlayerController.asset(widget.videoAssets)
          ..setLooping(true)
          ..setVolume(0)
          ..initialize().then((_) {
            _controller.play();
            setState(() {});
          });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_controller.value.isInitialized) {
      return Container(width: 360, height: 500, color: ColorOutlet.surface, child: Center(child: CircularProgressIndicator()));
    }

    return SizedBox(
      width: 360,
      height: 500,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: FittedBox(
          fit: BoxFit.cover,
          child: SizedBox(
            width: _controller.value.size.width,
            height: _controller.value.size.height,
            child: VideoPlayer(_controller),
          ),
        ),
      ),
    );
  }
}
