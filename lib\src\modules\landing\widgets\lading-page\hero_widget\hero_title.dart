import 'dart:math' as math;

import 'package:flutter/material.dart';

import '../../../../../components/responsive/responsive_scale_layout.dart';
import '../../../../../components/scroll_reveal.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../theme/color_outlet.dart';

class HeroTitle extends StatelessWidget {
  final double fontSize;
  final bool isMobile;
  const HeroTitle({super.key, this.fontSize = 64, this.isMobile = false});

  @override
  Widget build(BuildContext context) {
    return ResponsiveScaleLayout(
      builder: (context, screenWidth, itemWidth, scaleFactor) {
        final textScaler = MediaQuery.textScalerOf(context);

        return ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 530),
          child: Column(
            spacing: 16,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ScrollReveal(
                offset: Offset(0, -0.5),
                child: TextPattern.customText(
                  text: 'O app alerta, você economiza',
                  fontSize: isMobile ? math.max(32, textScaler.scale(fontSize * scaleFactor)) : fontSize,
                  fontWeightOption: FontWeightOption.bold,
                  color: ColorOutlet.contentTertiary,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              ScrollReveal(
                offset: Offset(0, -2),
                child: TextPattern.customText(
                  text: 'O Promobell é o app ideal para quem busca por produtos em ofertas, cupons de desconto e frete grátis.',
                  fontSize: isMobile ? math.max(14, textScaler.scale(20 * scaleFactor)) : 20,
                  fontWeightOption: FontWeightOption.regular,
                  color: ColorOutlet.contentTertiary,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
