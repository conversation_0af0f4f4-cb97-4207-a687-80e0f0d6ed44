import 'package:flutter/material.dart';

abstract class ColorOutlet {
  // Content
  static const Color contentPrimary = Color(0xFF3F3FE0);
  static const Color contentSecondary = Color(0xFF0F0F29);
  static const Color contentTertiary = Color(0xFFFCFCFC);
  static const Color contentDisabled = Color(0xFFC4C4CC);
  static const Color contentGhost = Color(0xFF89898F);
  static const Color hoverColor = Color(0xFFecebf5);

  // Environment
  static const Color surface = Color(0xFFF3F3F5);
  static const Color paper = Color(0xFFFFFFFF);
  static const Color filled = Color(0xFFFCFCFC);
  static const Color backgroundContainer = Color(0xFFf9f9fb);
  static const Color systemBorderDisabled = Color(0xFFD8D8E0);
  static const Color whatAppColor = Color(0xFF25D366);

  // Feedback
  static const Color feedbackSystem = Color(0xFF9999FF);
  static const Color feedbackInfo = Color(0xFF70CFFF);
  static const Color feedbackSuccess = Color(0xFF93F5A3);
  static const Color feedbackSuccessDarker = Color(0xFF64a76f);
  static const Color feedbackSuccessLight = Color(0xFFe9fded);
  static const Color feedbackWarning = Color(0xFFFFD070);
  static const Color feedbackError = Color(0xFFFF8599);
  static const Color feedbackAttention = Color(0xFFFFA970);
  static const Color feedbackDisabled = Color(0xFFC4C4CC);
}
