import '../../../../models/user_profile_model.dart';
import '../../../exceptions/app_exception.dart';
import '../../../logs/app_logger.dart';
import '../../connection/i_connection.dart';
import '../../db/db.dart';

class DeleteUsuarios {
  final IConnection _connection;
  final DB _db;

  DeleteUsuarios({
    required IConnection connection,
    required DB db,
  })  : _connection = connection,
        _db = db;

  Future<UserProfileModel> removeUserImage(String email) async {
    try {
      final data = await _connection.put(
        table: _db.tabelaDeUsuarios,
        body: {
          'image': null,
          'image_id': null,
        },
        filter: {'email': email},
      );

      if (data.isEmpty) {
        throw DatabaseException('Falha ao remover imagem do usuário');
      }

      return UserProfileModel.fromMap(data.first);
    } catch (e, stack) {
      AppLogger.logError('Erro ao remover imagem do usuário', e, stack);
      rethrow;
    }
  }
}
