import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/svg.dart';
import 'package:promobell/src/modules/offers/ui/widget/offers_page/debouncer.dart';
import 'package:promobell/src/services/navigation/scroll_services.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../core/base/controllers/base_controller.dart/base_controller.dart';
import '../../../controllers/offers_controller.dart';

// Controladores globais para manter o estado entre reconstruções
final _textController = TextEditingController();
final _focusNode = FocusNode();
final _debouncer = Debouncer(milliseconds: 700);

class CampoBuscar extends StatefulWidget {
  final bool isFixedFilter;
  const CampoBuscar({super.key, required this.isFixedFilter});

  @override
  State<CampoBuscar> createState() => _CampoBuscarState();
}

class _CampoBuscarState extends State<CampoBuscar>
    with AutomaticKeepAliveClientMixin {
  final OffersController controller = Modular.get<OffersController>();
  final BaseController baseController = Modular.get<BaseController>();
  final _scrollService = ScrollService();
  bool _isSearching = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    // Sincronizar o texto do controlador com a query de busca
    if (_textController.text != controller.searchQuery) {
      _textController.text = controller.searchQuery;
    }

    _textController.addListener(() {
      if (mounted) {
        setState(() {
          _isSearching = _textController.text.isNotEmpty;
        });
      }
    });

    baseController.pageIndexNotifier.addListener(_resetOnPageChange);
    Modular.to.addListener(_resetOnPageChange);
  }

  @override
  void dispose() {
    baseController.pageIndexNotifier.removeListener(
      _resetOnPageChange,
    );
    Modular.to.removeListener(_resetOnPageChange);
    super.dispose();
  }

  void _resetOnPageChange() {
    if (!Modular.to.path.contains('/offers')) {
      _focusNode.unfocus();
    }
  }

  void _handleSearch(String value) {
    _debouncer.run(() {
      controller.limparTodosOsFiltros(isCategoryFilters: false);
      controller.buscarProdutos(value);

      if (!_focusNode.hasFocus) {
        _focusNode.requestFocus();
      }

      final scrollController = _scrollService.getScrollController(
        'offersPage',
      );

      if (scrollController == null || !scrollController.hasClients) {
        return;
      }

      final offset = scrollController.offset;

      // Scroll apenas se necessário
      if (_focusNode.hasFocus && offset > 1.0) {
        Future.delayed(const Duration(milliseconds: 300), () {
          if (_focusNode.hasFocus && scrollController.hasClients) {
            scrollController.animateTo(
              0.0,
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeOut,
            );
          }
        });
      }

      // 🛠 reaplicar foco após rebuild (garante no iOS)
      Future.delayed(const Duration(milliseconds: 400), () {
        if (mounted && !_focusNode.hasFocus) {
          _focusNode.requestFocus();
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Container(
      width: double.infinity,
      alignment: Alignment.bottomCenter,
      child: Stack(
        alignment: Alignment.center,
        children: [
          if (widget.isFixedFilter)
            Container(
              height: 48,
              decoration: BoxDecoration(
                color: ColorOutlet.surface,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
            ),
          Align(
            alignment: Alignment.topCenter,
            child: Container(
              height: 35,
              decoration: BoxDecoration(color: ColorOutlet.surface),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: SizedBox(
              height: 48,
              width: double.infinity,
              child: TextField(
                controller: _textController,
                focusNode: _focusNode,
                onChanged: _handleSearch,
                onTapOutside: (event) => _focusNode.unfocus(),
                decoration: InputDecoration(
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 14,
                  ),
                  hintText:
                      'Encontre produtos, marcas e muito mais...',
                  hintStyle: TextStyle(
                    color: ColorOutlet.contentGhost,
                    fontSize: 14,
                    fontFamily: TextPattern().fontFamily,
                    fontWeight: FontWeight.w400,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: ColorOutlet.paper,
                  prefixIcon:
                      !_isSearching
                          ? Padding(
                            padding: EdgeInsets.only(
                              left: 12,
                              right: 8,
                            ),
                            child: SvgPicture.asset(
                              SvgIcons.actionSearch,
                              colorFilter: ColorFilter.mode(
                                ColorOutlet.contentSecondary,
                                BlendMode.srcIn,
                              ),
                              height: 24,
                              width: 24,
                            ),
                          )
                          : null,
                  suffixIcon:
                      _isSearching || _textController.text.isNotEmpty
                          ? IconButton(
                            icon: SvgPicture.asset(
                              SvgIcons.actionClose,
                              height: 24,
                              width: 24,
                            ),
                            color: ColorOutlet.contentSecondary,
                            onPressed: () {
                              _textController.clear();
                              _focusNode.unfocus();

                              // 🧹 Resetar tipo da lista para "geral"
                              controller.setTipoLista(
                                TipoListaProdutos.geral,
                              );

                              // 🧼 Limpa filtros e executa nova busca
                              controller.limparTodosOsFiltros(
                                isCategoryFilters: false,
                              );
                              controller.buscarProdutos('');

                              // ✅ Atualiza a UI
                              setState(() {
                                _isSearching = false;
                              });
                            },
                          )
                          : null,
                ),
                cursorColor: ColorOutlet.contentSecondary,
                style: TextStyle(
                  color: ColorOutlet.contentSecondary,
                  fontSize: 14,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
