// Este script ajuda os crawlers a indexar melhor o conteúdo do Flutter Web
(function () {
  // Detecta se o visitante é um crawler de mecanismo de busca
  function isCrawler() {
    return /bot|googlebot|crawler|spider|robot|crawling/i.test(navigator.userAgent);
  }

  // Adiciona conteúdo estático para crawlers
  function addStaticContent() {
    if (!isCrawler()) return;

    // Adiciona conteúdo estático para a página inicial
    if (window.location.pathname === '/' || window.location.pathname === '') {
      const mainContent = document.createElement('div');
      mainContent.style.display = 'none';
      mainContent.innerHTML = `
        <p>O Promobell é o app ideal para quem quer aproveitar ofertas de produtos, cupons de desconto e frete grátis das melhores lojas do Brasil, como Amazon, Mercado Livre e Magalu. Baixe agora na Google Play ou App Store e comece a economizar!</p>
        <h2>Baixe o app Promobell</h2>
        <p>Disponível para Android e iOS. Baixe agora e comece a economizar!</p>
        <h2>Principais recursos</h2>
        <ul>
          <li>Notificações de ofertas em tempo real</li>
          <li>Ofertas personalizadas baseadas nos seus interesses</li>
          <li>Economize dinheiro nas suas compras online</li>
          <li>Acesso às melhores promoções da Amazon, Mercado Livre e Magalu</li>
        </ul>
      `;
      document.body.appendChild(mainContent);
    }

    // Adiciona conteúdo estático para a página Sobre
    if (window.location.pathname === '/sobre') {
      const aboutContent = document.createElement('div');
      aboutContent.style.display = 'none';
      aboutContent.innerHTML = `
        <p>O Promobell é o app ideal para quem quer aproveitar ofertas de produtos, cupons de desconto e frete grátis das melhores lojas do Brasil, como Amazon, Mercado Livre e Magalu. Baixe agora na Google Play ou App Store e comece a economizar!</p>
        <p>Nossa missão é ajudar você a economizar dinheiro, encontrando as melhores promoções.</p>
      `;
      document.body.appendChild(aboutContent);
    }

    // Adiciona conteúdo estático para a página Termos
    if (window.location.pathname === '/termos') {
      const termsContent = document.createElement('div');
      termsContent.style.display = 'none';
      termsContent.innerHTML = `
        <h1>Termos de Uso</h1>
        <p>Estes termos de uso estabelecem as regras para utilização do aplicativo Promobell.</p>
      `;
      document.body.appendChild(termsContent);
    }

    // Adiciona conteúdo estático para a página Privacidade
    if (window.location.pathname === '/privacidade') {
      const privacyContent = document.createElement('div');
      privacyContent.style.display = 'none';
      privacyContent.innerHTML = `
        <h1>Política de Privacidade</h1>
        <p>A Política de Privacidade do Promobell descreve como coletamos, usamos e protegemos suas informações pessoais.</p>
      `;
      document.body.appendChild(privacyContent);
    }

    // Adiciona conteúdo estático para a página de Produto
    if (window.location.pathname === '/product') {
      const productContent = document.createElement('div');
      productContent.style.display = 'none';
      productContent.innerHTML = `
        <h1>Oferta Promobell</h1>
        <p>Confira esta oferta incrível encontrada pelo Promobell. O app alerta, você economiza!</p>
        <p>Baixe o aplicativo Promobell para receber notificações de ofertas como esta em tempo real.</p>
      `;
      document.body.appendChild(productContent);
    }

    // Adiciona conteúdo estático para a página de Categoria
    if (window.location.pathname === '/category') {
      const categoryContent = document.createElement('div');
      categoryContent.style.display = 'none';
      categoryContent.innerHTML = `
        <h1>Categoria Promobell</h1>
        <p>Confira as melhores ofertas desta categoria no Promobell. O app alerta, você economiza!</p>
        <p>Baixe o aplicativo Promobell para receber notificações de ofertas desta categoria em tempo real.</p>
      `;
      document.body.appendChild(categoryContent);
    }
  }

  // Executa quando o DOM estiver pronto
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addStaticContent);
  } else {
    addStaticContent();
  }
})();
