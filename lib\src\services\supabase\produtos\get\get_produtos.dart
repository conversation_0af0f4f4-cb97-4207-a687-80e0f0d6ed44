import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../models/product.dart';
import '../../db/db.dart';

class GetProdutos {
  final SupabaseClient _supabase = Supabase.instance.client;
  final DB _db = DB();

  Future<Product?> getProduto(int idProduto) async {
    try {
      final data =
          await _supabase
              .from(_db.tabelaDeProdutos)
              .select()
              .eq('id', idProduto)
              .eq('ativo', true)
              .single();
      final Product produto = Product.fromMap(data);
      return produto;
    } catch (e, stackTrace) {
      debugPrint(
        'Erro ao buscar produto'
        ' com id $idProduto: $e - $stackTrace',
      );
      rethrow;
    }
  }
}
