import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:meta_seo/meta_seo.dart';
import 'package:promobell_landing/src/components/text_pattern.dart';
import 'package:promobell_landing/src/modules/landing/widgets/onboarding_download_section.dart';
import 'package:promobell_landing/src/modules/landing/widgets/product/offer_platform_label.dart';
import 'package:promobell_landing/src/modules/landing/widgets/product/product_image_card.dart';
import 'package:promobell_landing/src/modules/landing/widgets/product/view_in_store_button.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../components/no_mouse_drag_scroll_behavior.dart';
import '../../../components/seo/seo.dart';
import '../../../models/product.dart';
import '../../../services/supabase/produtos/get/get_produtos.dart';
import '../../../theme/color_outlet.dart';
import '../widgets/lading-page/landing_header/landing_header.dart';
import 'empty_state.dart';

class ProductPage extends StatefulWidget {
  const ProductPage({super.key});

  @override
  State<ProductPage> createState() => _ProductPageState();
}

class _ProductPageState extends State<ProductPage> implements Seo {
  Product? _product;
  bool _isLoading = true;
  String? _errorMessage;

  String _formatCurrency(double value) {
    String priceString = value
        .toStringAsFixed(2)
        .replaceAll('.', ',');

    // Adiciona ponto para separar milhares
    final parts = priceString.split(',');
    final intPart = parts[0];
    final decimalPart = parts[1];

    // Formata a parte inteira com pontos a cada 3 dígitos
    String formattedIntPart = '';
    for (int i = 0; i < intPart.length; i++) {
      if (i > 0 && (intPart.length - i) % 3 == 0) {
        formattedIntPart += '.';
      }
      formattedIntPart += intPart[i];
    }

    return '$formattedIntPart,$decimalPart';
  }

  @override
  void initState() {
    super.initState();
    _loadProductData();
  }

  Future<void> _loadProductData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Obter o ID do produto da URL
      final uri = Uri.parse(Uri.base.toString());
      final productIdStr = uri.queryParameters['id'];

      if (productIdStr == null || productIdStr.isEmpty) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'ID do produto não fornecido';
        });
        return;
      }

      // Converter o ID para inteiro
      final productId = int.tryParse(productIdStr);
      if (productId == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'ID do produto inválido';
        });
        return;
      }

      // Buscar o produto do Supabase
      final getProdutos = Modular.get<GetProdutos>();
      final produto = await getProdutos.getProduto(productId);

      setState(() {
        _product = produto;
        _isLoading = false;
      });

      // Carregar as meta tags SEO
      loadSeo();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Erro ao carregar o produto: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    if (_errorMessage != null || _product == null) {
      return EmptyState();
    }

    return SelectionArea(
      child: Scaffold(
        backgroundColor: ColorOutlet.surface,
        body: ScrollConfiguration(
          behavior: NoMouseDragScrollBehavior(),
          child: Stack(
            children: [
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 96,
                  color: Color(0xFFFAFAFC).withValues(alpha: 80),
                ),
              ),
              CustomScrollView(
                slivers: [
                  const LandingHeader(center: true),
                  SliverToBoxAdapter(
                    child: Column(
                      children: [
                        Container(
                          color: ColorOutlet.paper,
                          child: FixedWidthFittedSection(
                            child: Column(
                              crossAxisAlignment:
                                  CrossAxisAlignment.start,
                              children: [
                                SizedBox(height: 32),
                                OfferPlatformLabel(
                                  plataforma: _product!.plataforma,
                                ),
                                SizedBox(height: 16),
                                ProductImageCard(
                                  urlImage: _product!.urlImagem,
                                ),
                                SizedBox(height: 16),
                                TextPattern.customText(
                                  text: _product!.titulo,
                                  fontSize: 16,
                                  fontWeightOption:
                                      FontWeightOption.semiBold,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                SizedBox(height: 16),
                                Column(
                                  crossAxisAlignment:
                                      CrossAxisAlignment.start,
                                  children: [
                                    if (_product!.precoAntigo > 0)
                                      TextPattern.customText(
                                        text: _formatCurrency(
                                          _product!.precoAntigo,
                                        ),
                                        fontSize: 14,
                                        color:
                                            ColorOutlet.contentGhost,
                                        decoration:
                                            TextDecoration
                                                .lineThrough,
                                      ),
                                    if (_product!.precoAtual > 0)
                                      TextPattern.customText(
                                        text: _formatCurrency(
                                          _product!.precoAtual,
                                        ),
                                        fontSize: 24,
                                        color:
                                            ColorOutlet
                                                .contentPrimary,
                                        fontWeightOption:
                                            FontWeightOption.bold,
                                        overflow:
                                            TextOverflow.ellipsis,
                                      ),
                                    if (_product!.precoAtual == 0)
                                      TextPattern.customText(
                                        text:
                                            'Produto não disponível',
                                        fontSize: 18,
                                        color:
                                            ColorOutlet
                                                .contentPrimary,
                                        fontWeightOption:
                                            FontWeightOption.medium,
                                      ),
                                  ],
                                ),
                                SizedBox(height: 16),
                                if (_product!.precoAtual > 0)
                                  ViewInStoreButton(
                                    onPressed: () {
                                      if (_product!
                                          .urlAfiliado
                                          .isNotEmpty) {
                                        launchUrl(
                                          Uri.parse(
                                            _product!.urlAfiliado,
                                          ),
                                        );
                                      }
                                    },
                                  ),
                                SizedBox(height: 32),
                              ],
                            ),
                          ),
                        ),
                        OnboardingDownloadSection(),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  loadSeo() {
    if (_product == null) return;

    final meta = MetaSEO();
    meta.ogTitle(ogTitle: _product!.titulo);
    meta.description(
      description:
          "${_product!.descricao} - O app alerta, você economiza",
    );
    meta.ogDescription(
      ogDescription:
          "${_product!.descricao} - O app alerta, você economiza",
    );
    meta.ogImage(ogImage: _product!.urlImagem);
    meta.propertyContent(property: 'og:type', content: 'product');
    meta.propertyContent(
      property: 'og:price:amount',
      content: _formatCurrency(_product!.precoAtual),
    );
    meta.propertyContent(
      property: 'og:price:currency',
      content: 'BRL',
    );
    meta.propertyContent(
      property: 'og:availability',
      content: _product!.precoAtual > 0 ? 'in_stock' : 'out_of_stock',
    );
    meta.propertyContent(
      property: 'og:url',
      content: 'https://promobell.com.br/product?id=${_product!.id}',
    );
    meta.keywords(
      keywords:
          'ofertas, cupons de desconto, frete grátis, alertas de preço, produtos em promoção, ${_product!.plataforma}, Amazon, Magalu, Mercado Livre, app de economia, descontos online, notificações de promoções, rede social de ofertas, ${_product!.categoria}, ${_product!.subcategoria}, buscar ofertas, economia inteligente, app para economizar, alertas inteligentes, cupons exclusivos, o app alerta você economiza, promobell',
    );

    // Adicionar descrição adicional para melhorar o SEO
    meta.propertyContent(
      property: 'og:site_name',
      content: 'Promobell - O app alerta, você economiza',
    );

    // Adicionar informações de preço com desconto
    if (_product!.precoAntigo > _product!.precoAtual) {
      final descontoPercentual = ((_product!.precoAntigo -
                  _product!.precoAtual) /
              _product!.precoAntigo *
              100)
          .toStringAsFixed(0);
      meta.propertyContent(
        property: 'product:price:savings',
        content: _formatCurrency(
          _product!.precoAntigo - _product!.precoAtual,
        ),
      );
      meta.propertyContent(
        property: 'product:price:savings_percentage',
        content: descontoPercentual,
      );

      // Adicionar informação de desconto na descrição
      final descricaoComDesconto =
          "${_product!.descricao} - Com $descontoPercentual% de desconto! O app alerta, você economiza";
      meta.description(description: descricaoComDesconto);
      meta.ogDescription(ogDescription: descricaoComDesconto);
    }
  }
}
