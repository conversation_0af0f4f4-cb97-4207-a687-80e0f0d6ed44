{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\TRABALHO\\PROMOBELL - LTDA\\promobell\\android\\app\\.cxx\\RelWithDebInfo\\5s4q1q3x\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\TRABALHO\\PROMOBELL - LTDA\\promobell\\android\\app\\.cxx\\RelWithDebInfo\\5s4q1q3x\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}