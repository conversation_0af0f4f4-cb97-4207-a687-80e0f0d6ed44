import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import '../../../../../theme/color_outlet.dart';
import '../../../../../theme/svg_icons.dart';
import '../../../../components/text_pattern.dart';

class FollowButton extends StatelessWidget {
  final bool isFollowing;
  final VoidCallback? onPressed;

  const FollowButton({
    required this.isFollowing,
    this.onPressed,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
          elevation: 0,
          shadowColor: Colors.transparent,
          backgroundColor: isFollowing ? ColorOutlet.feedbackDisabled : ColorOutlet.contentPrimary,
          overlayColor: ColorOutlet.systemBorderDisabled.withValues(alpha: 0.3),
          minimumSize: Size(151, 40),
          maximumSize: Size(151, 40),
          padding: isFollowing ? EdgeInsets.only(top: 10, bottom: 10, left: 28) : EdgeInsets.zero,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          if (!isFollowing) SizedBox(width: 50),
          if (isFollowing) SvgPicture.asset(SvgIcons.actionNotification, height: 20, width: 20),
          SizedBox(width: 8),
          TextPattern.customText(
            text: isFollowing ? 'Seguindo' : 'Seguir',
            fontSize: 14,
            color: isFollowing ? ColorOutlet.contentSecondary : ColorOutlet.contentTertiary,
          ),
          if (!isFollowing) SizedBox(width: 50),
        ],
      ),
    );
  }
}
