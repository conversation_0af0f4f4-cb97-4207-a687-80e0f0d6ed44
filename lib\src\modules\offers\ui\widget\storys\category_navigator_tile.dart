import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../core/base/controllers/base_controller.dart/base_controller.dart';
import '../../../../../models/categorias_menu.dart';
import '../../../../categories/categories_module.dart';
import '../../../controllers/offers_controller.dart';
import '../offers_page/category_image_badg.dart';

class CategoryNavigatorTile extends StatelessWidget {
  final BaseController controllerBase;
  final CategoriaMenu categoria;
  final OffersController controller;
  final DateTime criadoEm;
  final String followers;

  const CategoryNavigatorTile({
    super.key,
    required this.controllerBase,
    required this.controller,
    required this.criadoEm,
    required this.followers,
    required this.categoria,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        controllerBase.navPage(1);
        Modular.to.pushNamed(
          '/categories${CategoriesModule.detailsCategoryPage}',
          arguments: categoria,
        );
      },
      child: Row(
        children: [
          CategoryImageBadg(
            categoria: categoria,
            height: 48,
            width: 48,
            heightImage: 32,
            widthImage: 32,
            radius: 16,
          ),
          SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  TextPattern.customText(
                    text: categoria.nome,
                    fontSize: 20,
                    color: ColorOutlet.contentTertiary,
                    fontWeightOption: FontWeightOption.bold,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(width: 4),
                  SvgPicture.asset(
                    SvgIcons.markerVerifiedFilled,
                    colorFilter: ColorFilter.mode(
                      ColorOutlet.paper,
                      BlendMode.srcIn,
                    ),
                    height: 20,
                    width: 20,
                  ),
                  SizedBox(width: 4),
                  TextPattern.customText(
                    text: controller.getTimeToNowStory(criadoEm),
                    color: ColorOutlet.surface,
                    fontSize: 12,
                    fontWeightOption: FontWeightOption.regular,
                  ),
                ],
              ),
              TextPattern.customText(
                text:
                    '$followers  ${followers == '1' ? 'seguidor' : 'seguidores'}',
                fontSize: 12,
                color: ColorOutlet.contentTertiary,
                fontWeightOption: FontWeightOption.regular,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
