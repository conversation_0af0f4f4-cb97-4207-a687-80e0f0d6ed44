import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/modules/offers/controllers/story/story_controller.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/custom_snack_bar.dart';
import '../../../../../models/categorias_menu.dart';
import '../../../../offers/controllers/offers_controller.dart';
import '../../../controllers/categories_controller.dart';
import 'button_icon_with_background.dart';
import 'category_name.dart';
import 'follow_button.dart';

class AppBarTitleAnimations extends StatefulWidget {
  const AppBarTitleAnimations({super.key, required this.controller, required this.category});

  final CategoriesController controller;
  final CategoriaMenu category;

  @override
  State<AppBarTitleAnimations> createState() => _AppBarTitleAnimationsState();
}

class _AppBarTitleAnimationsState extends State<AppBarTitleAnimations> {
  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: widget.controller.opacity,
      duration: const Duration(milliseconds: 100),
      child: Transform.translate(
        offset: Offset(widget.controller.titleOffset, 0),
        child: CategoryName(categoria: widget.category, spacing: true, mainAxisAlignment: MainAxisAlignment.start),
      ),
    );
  }
}

class AnimatedActionsRow extends StatelessWidget {
  final CategoriesController controller;
  final OffersController offersController;

  final CategoriaMenu category;
  const AnimatedActionsRow({
    super.key,
    required this.controller,
    required this.offersController,
    required this.category,
  });

  @override
  Widget build(BuildContext context) {
    StoryController storyController = Modular.get<StoryController>();
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AnimatedOpacity(
          opacity: controller.opacity,
          duration: const Duration(milliseconds: 100),
          child: Transform.translate(
            offset: Offset(controller.followButtonOffset, 0),
            child: FollowButtonTop(
              isFollowing: offersController.isCategoryFollowed(category.id),
              onFollow: () {
                offersController
                    .toggleFollowCategory(
                      category.id,
                      Supabase.instance.client.auth.currentUser?.email ?? '',
                      category.nome,
                    )
                    .then((_) {
                      storyController.refreshStory();
                    });
              },
              onUnfollow: () {
                offersController
                    .toggleFollowCategory(
                      category.id,
                      Supabase.instance.client.auth.currentUser?.email ?? '',
                      category.nome,
                    )
                    .then((_) {
                      storyController.refreshStory();
                    });

                CustomSnackBar.show(
                  context: context,
                  message: "Você deixou de seguir esta categoria.",
                  icon: SvgIcons.feedbackCheck,
                );
              },
            ),
          ),
        ),
        AnimatedOpacity(
          opacity: controller.opacity,
          duration: const Duration(milliseconds: 100),
          child: ButtonIconWithBackground(
            iconPath: SvgIcons.actionShare,
            isSared: offersController.isLoadingShare,
            onPressed: () {
              offersController.shareCategory(category, isFollowing: offersController.isCategoryFollowed(category.id));
            },
          ),
        ),
      ],
    );
  }
}
