import 'package:flutter/material.dart';

import '../../../../../theme/color_outlet.dart';

class HeroImage extends StatelessWidget {
  final double? height;
  final bool isComplete;

  const HeroImage({super.key, this.height, this.isComplete = false});

  @override
  Widget build(BuildContext context) {
    return BorderedPanel(
      height: height,
      width: 422,
      isComplete: true,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(45),
        child: Container(color: ColorOutlet.surface, child: Image.asset('assets/images/frame.png', fit: BoxFit.cover)),
      ),
    );
  }
}

class BorderedPanel extends StatelessWidget {
  final double? height;
  final double width;
  final Widget? child;
  final Color color;
  final bool isComplete;

  const BorderedPanel({
    this.color = ColorOutlet.surface,
    this.height,
    required this.width,
    this.isComplete = false,
    this.child,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: isComplete ? 876 : height,
      width: width,
      decoration: BoxDecoration(
        color: color,
        borderRadius:
            isComplete
                ? BorderRadius.circular(64)
                : BorderRadius.only(topLeft: Radius.circular(64), topRight: Radius.circular(64)),
        border: Border.all(color: ColorOutlet.systemBorderDisabled, width: 16),
      ),
      child: child,
    );
  }
}
