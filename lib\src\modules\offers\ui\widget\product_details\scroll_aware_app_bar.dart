import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/components/custom_snack_bar.dart';

import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../core/base/controllers/base_controller.dart/base_controller.dart';
import '../../../../categories/ui/widgets/detail/button_icon_with_background.dart';
import '../../../controllers/offers_controller.dart';
import '../../../controllers/products_details_controller.dart';
import '../../pages/product_details_page.dart';

class ScrollAwareAppBar extends StatelessWidget {
  final ProductDetailsPage widget;
  final ProductDetailsController productDetailsController;
  const ScrollAwareAppBar({
    super.key,
    required this.widget,
    required this.productDetailsController,
  });

  @override
  Widget build(BuildContext context) {
    final BaseController controllerBase =
        Modular.get<BaseController>();
    final OffersController controller =
        Modular.get<OffersController>();

    return Column(
      children: [
        SizedBox(height: productDetailsController.topPadding),
        SizedBox(
          height: 56,
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ButtonIconWithBackground(
                      iconPath: SvgIcons.arrowClearLeft,
                      onPressed: () {
                        controllerBase.navPage(0);
                        Modular.to.pop();
                      },
                    ),
                    SizedBox(width: 10),
                    Container(
                      height: 40,
                      alignment: Alignment.center,
                      child: TextPattern.customText(
                        text: 'Oferta',
                        fontSize: 20,
                        fontWeightOption: FontWeightOption.bold,
                      ),
                    ),
                    Spacer(),
                    ButtonIconWithBackground(
                      iconPath:
                          controller.isProductSavedSync(
                                widget.product,
                              )
                              ? SvgIcons.markerBookmarkFilled
                              : SvgIcons.markerBookmark,
                      isSared: controller.isLoadingShare,
                      onPressed: () {
                        controller.toggleSavedProduct(widget.product);
                        CustomSnackBar.show(
                          context: context,
                          noBottomPadding: true,
                          message:
                              "Produto salvo na sua lista de ofertas",
                          icon: SvgIcons.feedbackCheck,
                        );
                      },
                    ),
                    SizedBox(width: 8),
                    ButtonIconWithBackground(
                      iconPath: SvgIcons.actionShare,
                      isSared: controller.isLoadingShare,
                      onPressed:
                          () => controller.launchWhatsApp(
                            widget.product,
                          ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
