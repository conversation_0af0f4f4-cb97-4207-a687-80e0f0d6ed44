import 'package:flutter/material.dart';

class ShadowContainer extends StatelessWidget {
  final double width;
  final double height;

  const ShadowContainer({
    required this.width,
    required this.height,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        color: Colors.transparent,
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF353551).withAlpha(40),
            offset: Offset(0, 0),
            blurRadius: 48,
            spreadRadius: 0,
          ),
        ],
      ),
    );
  }
}
