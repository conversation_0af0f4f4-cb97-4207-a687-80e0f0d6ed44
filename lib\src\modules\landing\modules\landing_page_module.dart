import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell_landing/src/modules/landing/controller/landing_page_controller.dart';

import '../controller/privacy_page_controller.dart';
import '../page/about_the_promobell.dart';
import '../page/category_page.dart';
import '../page/delete_acount_page.dart';
import '../page/landing_page.dart';
import '../page/privacy_page.dart';
import '../page/product_page.dart';
import '../page/terms_of_use.dart';

class LandingPageModule extends Module {
  @override
  final List<Bind> binds = [
    Bind.lazySingleton((i) => LandingPageController()),
    Bind.lazySingleton((i) => PrivacyPageController()),
  ];

  @override
  final List<ModularRoute> routes = [
    ChildRoute(
      '/',
      child: (_, args) => LandingPage(controller: Modular.get<LandingPageController>())..loadSeo(),
      transition: TransitionType.noTransition,
    ),
    ChildRoute(
      '/privacidade',
      child: (_, args) => PrivacyPage(controller: Modular.get<PrivacyPageController>())..loadSeo(),
      transition: TransitionType.noTransition,
    ),
    ChildRoute('/sobre', child: (_, args) => AboutThePromobell()..loadSeo(), transition: TransitionType.noTransition),
    ChildRoute('/termos', child: (_, args) => TermsOfUse()..loadSeo(), transition: TransitionType.noTransition),
    ChildRoute('/product', child: (_, args) => ProductPage(), transition: TransitionType.noTransition),
    ChildRoute('/category', child: (_, args) => CategoryPage(), transition: TransitionType.noTransition),
    ChildRoute('/delete-account', child: (_, args) => DeleteAccountPage(), transition: TransitionType.noTransition),
  ];
}
