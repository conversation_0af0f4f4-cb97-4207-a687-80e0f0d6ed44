import 'dart:ui';

class CategoriaMenu {
  final int id;
  final String nome;
  final String fotoPequena;
  final String? fotoGrande;
  final String? video;
  final Color cor;
  final String bio;

  CategoriaMenu({
    required this.id,
    required this.nome,
    required this.fotoPequena,
    required this.bio,
    this.fotoGrande,
    this.video,
    required this.cor,
  });

  static final List<CategoriaMenu> categorias = [
    CategoriaMenu(
      id: 1,
      nome: 'Espaço Decor',
      fotoPequena: 'assets/categorias/espaco_decor/category-image-espacobelo.png',
      fotoGrande: 'assets/categorias/tudo_tech/mobile_small.webp',
      video: 'assets/categorias/espaco_decor/espaco-decor.mp4',
      cor: const Color(0xFFB2A17D),
      bio: 'Promoções para tornar seu lar ainda mais aconchegante e funcional! 🏡🛋️',
    ),
    CategoriaMenu(
      id: 2,
      nome: 'Eletro Lar',
      fotoPequena: 'assets/categorias/eletro_lar/category-image-eletroelar.png',
      fotoGrande: 'assets/categorias/tudo_tech/mobile_small.webp',
      video: 'assets/categorias/eletro_lar/category-video-eletrolar-720p.mp4',
      cor: const Color(0xFF999999),
      bio: 'Promoções para tornar seu lar ainda mais aconchegante e funcional! 🏡🛋️',
    ),
    CategoriaMenu(
      id: 3,
      nome: 'Tudo Tech',
      fotoPequena: 'assets/categorias/tudo_tech/category-image-tudotech.png',
      fotoGrande: 'assets/categorias/tudo_tech/mobile_small.webp',
      video: 'assets/categorias/tudo_tech/category-video-tudotech-720p.mp4',
      cor: const Color(0xFFB88FCC),
      bio: 'Preços imperdíveis em TVs, áudio, informática, games e muito mais! 📺🎮💻',
    ),
    CategoriaMenu(
      id: 4,
      nome: 'Vibe Mobile',
      fotoPequena: 'assets/categorias/vibe_mobile/category-image-vibemobile.png',
      fotoGrande: 'assets/categorias/tudo_tech/mobile_small.webp',
      video: 'assets/categorias/vibe_mobile/vibe-mobile.mp4',
      cor: const Color(0xFF8FA3CC),
      bio: 'Os melhores preços em smartphones, smartwatches e acessórios! 📱⌚',
    ),
    CategoriaMenu(
      id: 5,
      nome: 'Beleza Pura',
      fotoPequena: 'assets/categorias/beleza_pura/category-image-belezapura.png',
      fotoGrande: 'assets/categorias/beleza_pura/cuidado_pessoal.png',
      video: 'assets/categorias/beleza_pura/category-video-belezapura-720p.mp4',
      cor: const Color(0xFFCC8F8F),
      bio: 'O melhor para você, com as melhores ofertas em beleza e cuidados pessoais! 🧴💆‍♀️',
    ),
    CategoriaMenu(
      id: 6,
      nome: 'Melhor Estilo',
      fotoPequena: 'assets/categorias/melhor_estilo/category-image-melhorestilo.png',
      fotoGrande: 'assets/categorias/melhor_estilo/melhor_estilo.png',
      video: 'assets/categorias/melhor_estilo/category-video-melhorestilo-720p.mp4',
      cor: const Color(0xFFBDCCA1),
      bio: 'Ofertas em moda e tendências para todos os estilos e guarda-roupas! 👕👟',
    ),
    CategoriaMenu(
      id: 7,
      nome: 'Mundo Baby',
      fotoPequena: 'assets/categorias/mundo_baby/category-image-probaby.png',
      fotoGrande: 'assets/categorias/tudo_tech/mobile_small.webp',
      video: 'assets/categorias/mundo_baby/mundo-baby.mp4',
      cor: const Color(0xFFCCB88F),
      bio: 'Do enxoval aos brinquedos, tudo com o melhor preço pro seu baby! 🍼🧸',
    ),
    CategoriaMenu(
      id: 8,
      nome: 'Vida Ativa',
      fotoPequena: 'assets/categorias/vida_ativa/category-image-vidaativa.png',
      fotoGrande: 'assets/categorias/tudo_tech/mobile_small.webp',
      video: 'assets/categorias/vida_ativa/category-video-vidaativa-720p.mp4',
      cor: const Color(0xFFA3BECC),
      bio: 'Descontos em itens e acessórios para quem leva um estilo de vida ativo. 🚴🏋️',
    ),
    CategoriaMenu(
      id: 9,
      nome: 'Nutri Day',
      fotoPequena: 'assets/categorias/nutri_day/category-image-nutriday.png',
      fotoGrande: 'assets/categorias/tudo_tech/mobile_small.webp',
      video: 'assets/categorias/nutri_day/category-video-nutriday-720p.mp4',
      cor: const Color(0xFF8FB8CC),
      bio: 'Para quem busca performance e resultados, ofertas exclusivas em suplementos. ⚡💪',
    ),
    CategoriaMenu(
      id: 10,
      nome: 'Mais Saúde',
      fotoPequena: 'assets/categorias/mais_saude/category-image-maissaude.png',
      fotoGrande: 'assets/categorias/tudo_tech/mobile_small.webp',
      video: 'assets/categorias/mais_saude/category-video-maissaude-720p.mp4',
      cor: const Color(0xFF7DB2AA),
      bio: 'Preços incríveis em acessórios e produtos para cuidados com sua saúde. 🩺🌡',
    ),
    CategoriaMenu(
      id: 11,
      nome: 'Brinde Perfeito',
      fotoPequena: 'assets/categorias/brinde_perfeito/category-image-habitosdiarios.png',
      fotoGrande: 'assets/categorias/tudo_tech/mobile_small.webp',
      video: 'assets/categorias/brinde_perfeito/brinde-perfeito.mp4',
      cor: const Color(0xFFCC8FB8),
      bio: 'Ofertas especiais na dose certa para que busca as melhores bebidas. 🍷🍺🔞',
    ),
    CategoriaMenu(
      id: 12,
      nome: 'Oficina Mix',
      fotoPequena: 'assets/categorias/oficina_mix/category-image-ofcinashow.png',
      fotoGrande: 'assets/categorias/tudo_tech/mobile_small.webp',
      video: 'assets/categorias/oficina_mix/oficina-mix.mp4',
      cor: const Color(0xFFCCAA66),
      bio: 'Ferramentas e acessórios com preços incríveis para deixar sua oficina completa! 🧰 🛠️',
    ),
    CategoriaMenu(
      id: 13,
      nome: 'Sobre Rodas',
      fotoPequena: 'assets/categorias/sobre_rodas/category-image-sobrerodas.png',
      fotoGrande: 'assets/categorias/tudo_tech/mobile_small.webp',
      video: 'assets/categorias/sobre_rodas/category-video-sobrerodas-720p.mp4',
      cor: const Color(0xFF8FA3CC),
      bio: 'Para apaixonados por velocidade e estrada, preços de tirar o fôlego! 🚗🏍️',
    ),
    CategoriaMenu(
      id: 14,
      nome: 'Tempo Livre',
      fotoPequena: 'assets/categorias/tempo_livre/category-image-tempolivre.png',
      fotoGrande: 'assets/categorias/tudo_tech/mobile_small.webp',
      video: 'assets/categorias/tempo_livre/tempo-livre.mp4',
      cor: const Color(0xFFADCC8F),
      bio:
          'Tudo o que você procura com desconto para transformar sua rotina e elevar seus hábitos! 📚🧘',
    ),
    CategoriaMenu(
      id: 15,
      nome: 'Melhor Amigo',
      fotoPequena: 'assets/categorias/melhor_amigo/category-image-melhoramigo.png',
      fotoGrande: 'assets/categorias/tudo_tech/mobile_small.webp',
      video: 'assets/categorias/melhor_amigo/category-video-melhoramigo-720p.mp4',
      cor: const Color(0xFFCCA38F),
      bio: 'Tudo que seu pet precisa para viver feliz ao seu lado, com descontos incríveis! 🐶🐱',
    ),
  ];

  static CategoriaMenu? getCategoriaById(int id) {
    try {
      return categorias.firstWhere((categoria) => categoria.id == id);
    } catch (e) {
      return null;
    }
  }

  static CategoriaMenu getCategoriaByNome(String nome) {
    return categorias.firstWhere(
      (categoria) => categoria.nome == nome,
      orElse: () => categorias.firstWhere((cat) => cat.nome == 'Espaço Decor'),
    );
  }
}
