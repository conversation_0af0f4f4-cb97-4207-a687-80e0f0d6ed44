import 'package:flutter/material.dart';

import '../../../../../models/story_model.dart';
import '../../../controllers/offers_controller.dart';
import '../../../controllers/products_details_controller.dart';
import '../../../controllers/story/story_controller.dart';
import '../../../controllers/story/story_navigation_controller.dart';
import 'card_products.dart';

class StoryPageView extends StatelessWidget {
  const StoryPageView({
    super.key,
    required this.navigationController,
    required this.stories,
    required this.topPadding,
    required this.productDetailsController,
    required this.controller,
    required this.storyController,
  });

  final StoryNavigationController navigationController;
  final List<StoryModel> stories;
  final double topPadding;
  final ProductDetailsController productDetailsController;
  final OffersController controller;
  final StoryController storyController;

  @override
  Widget build(BuildContext context) {
    return PageView.builder(
      physics: NeverScrollableScrollPhysics(),
      controller: navigationController.storyPageController,
      itemCount: stories.isEmpty ? 0 : stories.length,
      onPageChanged: navigationController.handlePageChanged,
      itemBuilder: (context, index) {
        if (index < 0 || index >= stories.length) {
          return const SizedBox();
        }

        return Padding(
          padding: EdgeInsets.only(left: 16, right: 16, top: topPadding + 100),
          child: Column(
            children: [
              CardProducts(
                productDetailsController: productDetailsController,
                story: stories[index],
                controller: controller,
                storyController: storyController,
              ),
            ],
          ),
        );
      },
    );
  }
}
